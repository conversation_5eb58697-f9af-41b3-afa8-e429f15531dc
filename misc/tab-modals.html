
<!DOCTYPE html>
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en-gb" lang="en-gb" dir="ltr">
  <head>
    <style>
      body:not(.mm-wrapper) #mobile-menu {
        display: none;
      }

      .mm-menu {
        --mm-color-background: #333;
        --mm-color-text: #fff;
        --mm-color-button: #fff;
      }

      #mobile-menu {
        background: var(--mm-color-background);
        color: var(--mm-color-text);
      }
    </style>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no" />
    <link rel="apple-touch-icon" sizes="180x180" href="/templates/zenbase/images/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/templates/zenbase/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="/templates/zenbase/images/favicon-16x16.png">
    <link rel="manifest" href="/templates/zenbase/images/site.webmanifest">
    <link rel="mask-icon" href="/templates/zenbase/images/safari-pinned-tab.svg" color="#0095cc">
    <!-- Individual Font Files -->
<link rel="prefetch" href="/templates/zenbase/fonts/IBMPlexSans-Light.eot" as="font" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/IBMPlexSans-Light.svg" as="font" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/IBMPlexSans-Light.ttf" as="font" type="font/ttf" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/IBMPlexSans-Light.woff" as="font" type="font/woff" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/IBMPlexSans-SemiBold.eot" as="font" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/IBMPlexSans-SemiBold.svg" as="font" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/IBMPlexSans-SemiBold.ttf" as="font" type="font/ttf" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/IBMPlexSans-SemiBold.woff" as="font" type="font/woff" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/OldGrowth-Regular.eot" as="font" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/OldGrowth-Regular.svg" as="font" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/OldGrowth-Regular.ttf" as="font" type="font/ttf" crossorigin="anonymous">
<link rel="prefetch" href="/templates/zenbase/fonts/OldGrowth-Regular.woff" as="font" type="font/woff" crossorigin="anonymous">    <meta name="msapplication-TileColor" content="#2b5797">
    <meta name="theme-color" content="#ffffff">
    <base href="https://evertrek.co.uk/" />
	<meta http-equiv="content-type" content="text/html; charset=utf-8" />
	<meta name="description" content="Evertrek Home - Everest Base Camp Trekking" />
	<meta name="generator" content="TravelZen 3.10.20-elts - Specialist Travel Content Management (www.mrzen.co.uk)" />
	<title>EverTrek | Home - EverTrek</title>
	<link href="/templates/zenbase/favicon.ico" rel="shortcut icon" type="image/vnd.microsoft.icon" />
	<link href="/components/com_sppagebuilder/assets/css/font-awesome-5.min.css" rel="stylesheet" type="text/css" />
	<link href="/components/com_sppagebuilder/assets/css/font-awesome-v4-shims.css" rel="stylesheet" type="text/css" />
	<link href="/components/com_sppagebuilder/assets/css/sppagebuilder.css" rel="stylesheet" type="text/css" />
	<link href="/components/com_sppagebuilder/assets/css/magnific-popup.css" rel="stylesheet" type="text/css" />
	<link href="/templates/zenbase/sppagebuilder/addons/holidaytabs/css/styles.css" rel="stylesheet" type="text/css" />
	<link href="/media/com_rstbox/css/engagebox.css?********************************" rel="stylesheet" type="text/css" />
	<link href="/templates/zenbase/css/bootstrap.css" rel="stylesheet" type="text/css" />
	<link href="/templates/zenbase/css/template.css" rel="stylesheet" type="text/css" />
	<style type="text/css">
.sp-page-builder .page-content #section-id-1739546099254{padding-top:150px;padding-right:0px;padding-bottom:50px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-image:url(https://i.assetzen.net/i/T62x345BAv7F/w:2323/h:1550/q:80.jpg);background-repeat:no-repeat;background-size:cover;background-attachment:scroll;background-position:100% 30%;box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1739546099254 > .sppb-row-overlay {mix-blend-mode:normal;}#column-id-1739546099251{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1739546099257 {margin:0px 0px 30px 0px;}#sppb-addon-1739546099257 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1739546099257 h1{font-family:'fustatregular', serif;font-size:36px;line-height:42px;color:black;font-weight:800;}@media screen and (min-width:576px){#sppb-addon-1739546099257 h1{font-size:48px;line-height:50px;}}#sppb-addon-wrapper-1739546099266 {margin:0px 0px 30px 0px;}#sppb-addon-1739546099266 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1739546099266 .btn-primary:first-of-type{margin-right:24px;}#sppb-addon-1739546099266 .btn-primary{margin-bottom:15px;}#sppb-addon-wrapper-1739546099263 {margin:0px 0px 30px 0px;}#sppb-addon-1739546099263 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1739546099263 a{font-size:16px;line-height:normal;font-weight:700;color:black;text-decoration:none;display:inline-flex;align-items:center;gap:8px;}#sppb-addon-wrapper-1739546099283 {margin:0px 0px 30px 0px;}#sppb-addon-1739546099283 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1739546099283 img{filter:drop-shadow(0 0 5px rgba(255, 255, 255, 0.5));}#sppb-addon-1739546099283 img{width:344px;max-width:344px;}#column-id-1739546099252{box-shadow:0 0 0 0 #fff;}.sp-page-builder .page-content #section-id-1743585701437{padding-top:150px;padding-right:0px;padding-bottom:50px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-image:url(/templates/zenbase/images/1064b4199bb544d41b0b65014c957906_o.jpg);background-repeat:no-repeat;background-size:cover;background-attachment:scroll;background-position:100% 70%;box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1743585701437 > .sppb-row-overlay {mix-blend-mode:normal;}#column-id-1743585701438{box-shadow:0 0 0 0 #fff;}#column-id-1743585701439{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1743585701440 {margin:0px 0px 30px 0px;}#sppb-addon-1743585701440 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743585701440 h1{font-family:'fustatregular', serif;font-size:36px;line-height:42px;color:black;font-weight:800;}@media screen and (min-width:576px){#sppb-addon-1743585701440 h1{font-size:48px;line-height:50px;}}#sppb-addon-wrapper-1743585701441 {margin:0px 0px 0px 0px;}#sppb-addon-1743585701441 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743585701441 a{font-size:16px;line-height:normal;font-weight:700;color:black;text-decoration:none;display:inline-flex;align-items:center;gap:8px;}#sppb-addon-wrapper-1743585701442 {margin:5px 5px 5px 5px;}#sppb-addon-1743585701442 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743585701442 img{margin-right:15px;}#sppb-addon-1743585701442 p{font-weight:600;display:flex;align-items:center;}#sppb-addon-wrapper-1743585701443 {margin:0px 0px 30px 0px;}#sppb-addon-1743585701443 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743585701443 .btn-primary:first-of-type{margin-right:24px;}#sppb-addon-1743585701443 .btn-primary{margin-bottom:15px;}#column-id-1743585701446{box-shadow:0 0 0 0 #fff;}.sp-page-builder .page-content #section-id-1733150149652{padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-color:#23383C;box-shadow:0 0 0 0 #ffffff;}#column-id-1733150149657{padding-top:0;padding-right:0;padding-bottom:0;padding-left:0;box-shadow:0 0 0 0 #fff;}#column-wrap-id-1733150149657{margin-top:0;margin-right:0;margin-bottom:0;margin-left:0;}#sppb-addon-wrapper-1736167383821 {margin:0px 0px 0px 0px;}#sppb-addon-1736167383821 {box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1743598653187{padding-top:50px;padding-right:0px;padding-bottom:50px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-image:url(/templates/zenbase/images/AdobeStock_310360177_lg_2.jpg);background-repeat:no-repeat;background-size:cover;background-attachment:scroll;background-position:50% 50%;box-shadow:0 0 0 0 #ffffff;}@media (max-width:767px) { .sp-page-builder .page-content #section-id-1743598653187{padding-top:35px;padding-right:0px;padding-bottom:35px;padding-left:0px;} }.sp-page-builder .page-content #section-id-1743598653187 > .sppb-row-overlay {mix-blend-mode:normal;}#column-id-1743598653188{box-shadow:0 0 0 0 #fff;}#column-id-1743598653188 > .sppb-column-overlay {mix-blend-mode:normal;}.sp-page-builder .page-content #section-id-1743598653189{padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;box-shadow:0 0 0 0 #ffffff;}#column-id-1743598653190{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1743598653191 {margin:0px 0px 0px 0px;}#sppb-addon-1743598653191 {box-shadow:0 0 0 0 #ffffff;}@media (max-width:767px) {#sppb-addon-wrapper-1743598653191 {margin-top:0px;margin-right:0px;margin-bottom:30px;margin-left:0px;}}#sppb-addon-1743598653191 h2,#sppb-addon-1743598653191 p{color:white;}@media screen and (min-width:576px){#sppb-addon-1743598653191 h2{margin-bottom:35px;}}#sppb-addon-wrapper-1743598653192 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653192 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743598653192 img{width:100%;}#sppb-addon-wrapper-1743598653194 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653194 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-wrapper-1743598653195 {margin:0px 0px 0px 0px;}#sppb-addon-1743598653195 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743598653195 h3,#sppb-addon-1743598653195 p{color:white;}#sppb-addon-wrapper-1743598653196 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653196 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743598653196 h3,#sppb-addon-1743598653196 p{color:white;}#sppb-addon-wrapper-1743598653198 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653198 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-wrapper-1743598653199 {margin:0px 0px 0px 0px;}#sppb-addon-1743598653199 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743598653199 h3,#sppb-addon-1743598653199 p{color:white;}#sppb-addon-wrapper-1743598653200 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653200 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743598653200 h3,#sppb-addon-1743598653200 p{color:white;}#sppb-addon-wrapper-1743598653202 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653202 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-wrapper-1743598653203 {margin:0px 0px 0px 0px;}#sppb-addon-1743598653203 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743598653203 h3,#sppb-addon-1743598653203 p{color:white;}#sppb-addon-wrapper-1743598653204 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653204 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743598653204 h3,#sppb-addon-1743598653204 p{color:white;}#sppb-addon-wrapper-1743598653206 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653206 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-wrapper-1743598653207 {margin:0px 0px 0px 0px;}#sppb-addon-1743598653207 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743598653207 h3,#sppb-addon-1743598653207 p{color:white;}#sppb-addon-wrapper-1743598653208 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653208 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1743598653208 h3,#sppb-addon-1743598653208 p{color:white;}#sppb-addon-wrapper-1743598653210 {margin:0px 0px 30px 0px;}#sppb-addon-1743598653210 {box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1689260044302{padding-top:78px;padding-right:0px;padding-bottom:78px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-color:#E3E8EE;box-shadow:0 0 0 0 #ffffff;}@media (max-width:767px) { .sp-page-builder .page-content #section-id-1689260044302{padding-top:35px;padding-right:0px;padding-bottom:0;padding-left:0px;} }#column-id-1689260044300{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1739546099202 {margin:0px 0px 0px 0px;}#sppb-addon-1739546099202 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1739546099202 p{margin-bottom:0;}@media screen and (min-width:576px){#sppb-addon-1739546099202 p{margin-bottom:20px;}}#sppb-addon-wrapper-1743751726697 {margin:0px 0px 0px 0px;}#sppb-addon-1743751726697 {box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1743597166109{padding-top:40px;padding-right:0px;padding-bottom:40px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-color:#FE7720;box-shadow:0 0 0 0 #ffffff;}@media (max-width:767px) { .sp-page-builder .page-content #section-id-1743597166109{padding-top:30px;padding-right:0px;padding-bottom:30px;padding-left:0px;} }#column-id-1743597166110{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1743597166112 {margin:0px 0px 0px 0px;}#sppb-addon-1743597166112 {box-shadow:0 0 0 0 #ffffff;}#column-id-1743597166111{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1743597166115 {margin:0px 0px 30px 0px;}#sppb-addon-1743597166115 {box-shadow:0 0 0 0 #ffffff;}@media screen and (max-width:767.98px){#sppb-addon-1743597166115 img{display:block;width:90%;margin:0 auto 30px auto;}#sppb-addon-1743597166115 img:last-of-type{margin-bottom:0;}}@media screen and (min-width:768px){#sppb-addon-1743597166115 .nudge-left{margin-left:-6%;}#sppb-addon-1743597166115 img{width:53%;}}#sppb-addon-wrapper-1743597166116 {margin:0px 0px 0px 0px;}#sppb-addon-1743597166116 {box-shadow:0 0 0 0 #ffffff;}@media screen and (max-width:767.98px){#sppb-addon-1743597166116 #experts-container{width:90%;}}.sp-page-builder .page-content #section-id-1739535159935{padding-top:50px;padding-right:0px;padding-bottom:50px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-color:#000000;background-image:url(/templates/zenbase/images/AdobeStock_310360177_lg_2.jpg);background-repeat:no-repeat;background-size:cover;background-attachment:scroll;background-position:50% 50%;box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1739535159935 > .sppb-row-overlay {mix-blend-mode:normal;}#column-id-1739535159927{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1739546099214 {margin:0px 0px 30px 0px;}#sppb-addon-1739546099214 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1739546099214 div.community-intro{padding-bottom:20px;border-bottom:1px solid #FE7720;margin-bottom:20px;overflow:hidden;display:flex;flex-direction:column;justify-content:space-between;}#sppb-addon-1739546099214 .awards{max-width:344px;height:auto;width:100%;}#sppb-addon-1739546099214 h2,#sppb-addon-1739546099214 p{color:white;}@media screen and (min-width:576px){#sppb-addon-1739546099214 div.community-intro{width:100%;flex-direction:row;}#sppb-addon-1739546099214 .community-intro-col{margin-right:20px;}}#column-id-1739535159928{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1739546099220 {margin:0px 0px 30px 0px;}#sppb-addon-1739546099220 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1739546099220 h3,#sppb-addon-1739546099220 p{color:white;}#sppb-addon-wrapper-1739546099228 {margin:0px 0px 30px 0px;}#sppb-addon-1739546099228 {box-shadow:0 0 0 0 #ffffff;}#column-id-1739535159929{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1739546099223 {margin:0px 0px 30px 0px;}#sppb-addon-1739546099223 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1739546099223 h3,#sppb-addon-1739546099223 p{color:white;}#sppb-addon-wrapper-1739546099231 {margin:0px 0px 30px 0px;}#sppb-addon-1739546099231 {box-shadow:0 0 0 0 #ffffff;}#column-id-1739535159930{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1739546099234 {margin:0px 0px 0px 0px;}#sppb-addon-1739546099234 {box-shadow:0 0 0 0 #ffffff;}#sppb-addon-1739546099234 h3,#sppb-addon-1739546099234 p{color:white;}#sppb-addon-1739546099234 .fb-join{margin-left:20px;position:relative;top:-5px;}#sppb-addon-1739546099234 .sub-title{margin:0 auto;}@media screen and (min-width:576px){#sppb-addon-1739546099234 .sub-title{display:flex;align-items:flex-start;}}#sppb-addon-wrapper-1743751726690 {margin:0px 0px 30px 0px;}#sppb-addon-1743751726690 {box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1739546099341{padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;box-shadow:0 0 0 0 #ffffff;}#column-id-1739546099340{box-shadow:0 0 0 0 #fff;}#sppb-addon-wrapper-1743751726685 {margin:0px 0px 0px 0px;}#sppb-addon-1743751726685 {box-shadow:0 0 0 0 #ffffff;}
.eb-5 .eb-dialog{--eb-max-width:500px;--eb-height:auto;--eb-padding:30px;--eb-border-radius:10px;--eb-background-color:rgba(255, 255, 255, 1);--eb-text-color:rgba(51, 51, 51, 1);--eb-dialog-shadow:var(--eb-shadow-none);text-align:left;--eb-border-style:solid;--eb-border-color:rgba(0, 0, 0, 0.4);--eb-border-width:1px;--eb-overlay-enabled:1;--eb-overlay-clickable:1;--eb-bg-image-enabled:0;--eb-background-image:none;}.eb-5 .eb-close{--eb-close-button-inside:block;--eb-close-button-outside:none;--eb-close-button-icon:block;--eb-close-button-image:none;--eb-close-button-font-size:30px;--eb-close-button-color:rgba(136, 136, 136, 1);--eb-close-button-hover-color:rgba(85, 85, 85, 1);}.eb-5 .eb-dialog .eb-container{justify-content:flex-start;min-height:100%;display:flex;flex-direction:column;}.eb-5 .eb-backdrop{--eb-overlay-background-color:rgba(0, 0, 0, 0.5);}.eb-5.eb-inst{justify-content:center;align-items:center;}@media screen and (max-width: 1024px){.eb-5 .eb-dialog{--eb-bg-image-enabled:inherit;--eb-background-image:none;}}@media screen and (max-width: 575px){.eb-5 .eb-dialog{--eb-bg-image-enabled:inherit;--eb-background-image:none;}}
.eb-5 {
                --animation_duration: 300ms;

            }
        
	</style>
	<script type="application/json" class="joomla-script-options new">{"csrf.token":"58a0fd930a0e743e8fe94b3480c67259","system.paths":{"root":"","base":""}}</script>
	<script src="/media/jui/js/jquery.min.js?********************************" type="text/javascript"></script>
	<script src="/media/jui/js/jquery-noconflict.js?********************************" type="text/javascript"></script>
	<script src="/media/jui/js/jquery-migrate.min.js?********************************" type="text/javascript"></script>
	<script src="/components/com_sppagebuilder/assets/js/jquery.parallax.js?efcb014a6f41a6d880968ea58653b58d" type="text/javascript"></script>
	<script src="/components/com_sppagebuilder/assets/js/sppagebuilder.js?efcb014a6f41a6d880968ea58653b58d" defer="defer" type="text/javascript"></script>
	<script src="/components/com_sppagebuilder/assets/js/jquery.magnific-popup.min.js" type="text/javascript"></script>
	<script src="/templates/zenbase/sppagebuilder/addons/holidaytabs/js/equalizer.js" type="text/javascript"></script>
	<script src="/media/system/js/core.js?********************************" type="text/javascript"></script>
	<script src="/media/com_rstbox/js/vendor/velocity.min.js?********************************" type="text/javascript"></script>
	<script src="/media/com_rstbox/js/vendor/velocity.ui.min.js?********************************" type="text/javascript"></script>
	<script src="/media/com_rstbox/js/engagebox.js?********************************" type="text/javascript"></script>
	<script src="/templates/zenbase/js/css_browser_selector.js" type="text/javascript"></script>
	<script src="/templates/zenbase/js/popper.min.js" type="text/javascript"></script>
	<script src="/templates/zenbase/js/bootstrap/bootstrap.min.js" type="text/javascript"></script>
	<script src="/templates/zenbase/js/styledSelects.js" type="text/javascript"></script>
	<script src="/templates/zenbase/js/slick.min.js" type="text/javascript"></script>
	<script src="/templates/zenbase/js/deep-link-to-tabs-v2.js" type="text/javascript"></script>
	<script src="/templates/zenbase/js/deep-link-button-handlers.js" type="text/javascript"></script>
	<script src="/templates/zenbase/js/additional.js" type="text/javascript"></script>
	<script src="/templates/zenbase/js/template.js" type="text/javascript"></script>
	<script src="https://www.google.com/recaptcha/api.js?render=6LfY46UfAAAAALT-6EEDz_RtTprMbVluZV7FJ1BD" type="text/javascript"></script>
	<script type="text/javascript">
;(function ($) {
	$.ajaxSetup({
		headers: {
			'X-CSRF-Token': Joomla.getOptions('csrf.token')
		}
	});
})(jQuery);
			window.ajax_base_path = '';
		
 try { 
document.addEventListener('DOMContentLoaded', () => {

    const path = window.location.pathname;
    console.log(path);
    /* if (
		path === "/toubkal-moroccan-hammam-experience"
    ) {
        console.log('>> campaign page');
        document.body.classList.add("hide-top-bar");
		document.querySelector('html').style.marginTop = '0';
    } */

    // Set the offset you want for consistent positioning
    const customOffset = 260;

    // Function to handle the scrolling once the .show class is detected
    const scrollToTarget = () => {
        const targetElement = document.querySelector('.tab-pane.show > .zen-holiday__content-item h2');
        if (targetElement) {
            const elementPosition = targetElement.getBoundingClientRect().top;
            const currentScrollY = window.scrollY;
            const yPosition = currentScrollY + elementPosition - customOffset;

            console.log('Scrolling to position:', yPosition);
            console.log('Element Position (relative to viewport):', elementPosition);
            console.log('Current ScrollY (page offset):', currentScrollY);
            console.log('Custom Offset:', customOffset);

            window.scrollTo({ top: yPosition, behavior: 'smooth' });
        } else {
            console.log('Target element not found after .show was added.');
        }
    };

    // Observe changes to detect when the .show class is applied
    const observer = new MutationObserver((mutationsList) => {
        for (const mutation of mutationsList) {
            if (
                mutation.type === 'attributes' &&
                mutation.attributeName === 'class' &&
                mutation.target.classList.contains('show')
            ) {
                scrollToTarget();
                break; // Stop after the first .show detection
            }
        }
    });

    // Select all elements with the .zen-tab__link class
    document.querySelectorAll('.zen-tab__link').forEach(item => {
        item.addEventListener('click', (event) => {
            console.log('Tab clicked:', item);
            event.preventDefault();

            // Start observing mutations on all tab-pane elements
            document.querySelectorAll('.tab-pane').forEach(pane => {
                observer.observe(pane, { attributes: true });
            });
        });
    });

});
 } catch { 
 console.log('JS error in Wyld Custom Code Module'); 
} 

if (typeof window.grecaptcha !== 'undefined') { grecaptcha.ready(function() { grecaptcha.execute("6LfY46UfAAAAALT-6EEDz_RtTprMbVluZV7FJ1BD", {action:'homepage'});}); }
 try { 
// Start countdown based on the target end date/time
//window.onload = function() {
  document.addEventListener( 'DOMContentLoaded', function() {

    window.cdTimer = new easytimer.Timer();
    const targetDate = '4/30/2025 23:59:59';  // Your target end date and time, mm/dd/yyyy hh:mm:ss
    // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
    const timeDifferenceInSeconds = calculateTimeDifference(targetDate);
  
    if (timeDifferenceInSeconds > 0) {
      // Start countdown using the time difference in seconds
      window.cdTimer.start({ countdown: true, startValues: { seconds: timeDifferenceInSeconds } });
      updateDisplay();  // Display initial time
    } else {
      // If the target date is in the past, handle the expiry immediately
      onCountdownExpiry();
    }
  // window.cdTimer.stop();
  
  // Function to handle countdown expiry (KABOOM event)
  function onCountdownExpiry() {
    var msg = "Sorry, offer ended!";
    console.log(msg);
    document.querySelectorAll('.campaign-end-date').forEach((element) => {
		element.innerHTML = msg;
	});
  }
  
  // Format the time as dd:hh:mm:ss
  function formatTimeValues(timerValues) {
    const days = timerValues.days < 10 ? '0' + timerValues.days : timerValues.days;
    const hours = timerValues.hours < 10 ? '0' + timerValues.hours : timerValues.hours;
    const minutes = timerValues.minutes < 10 ? '0' + timerValues.minutes : timerValues.minutes;
    const seconds = timerValues.seconds < 10 ? '0' + timerValues.seconds : timerValues.seconds;
    return `<span class="campaign-clock"><span class="digit days">${days}</span>:<span class="digit hours">${hours}</span><span class="delimiter">:</span><span class="digit minutes">${minutes}</span><span class="delimiter">:</span><span class="digit seconds">${seconds}</span></span> <!--left!-->`;
  }
  
  // Calculate the difference in seconds between now and the target date/time
  function calculateTimeDifference(targetDate) {
    const now = new Date();
    const endDate = new Date(targetDate);
    const timeDifference = endDate - now; // Difference in milliseconds
    return Math.floor(timeDifference / 1000); // Convert to seconds
  }
  
  // Update the displayed time every second
  window.cdTimer.addEventListener('secondsUpdated', function () {
    updateDisplay();
  });
  
  // Handle countdown expiry (when the countdown reaches zero)
  window.cdTimer.addEventListener('targetAchieved', function () {
    onCountdownExpiry();  // Call the expiry callback function
  });
  
  // Update the display with the formatted time
  function updateDisplay() {
    const formattedTime = formatTimeValues(window.cdTimer.getTimeValues());
    document.querySelectorAll('.campaign-end-date').forEach((element) => {
		element.innerHTML = formattedTime;
	});
  }
  
});
  
  
 } catch { 
 console.log('JS error in Wyld Custom Code Module'); 
} 

 try { 
document.addEventListener('DOMContentLoaded', () => {
    const customOffset = 350; // Adjust scroll position for fixed headers
    const priceUpdateEventName = 'priceUpdateComplete'; // Custom event name
    let isScrolling = false; // Scroll state flag
    let lastScrolledId = null; // Track the last scrolled-to ID
    let debounceTimer = null; // Timer for debounce logic

    // Function to parse the fragment
    const parseFragment = () => {
        const hash = window.location.hash.substring(1); // Remove the # character
        if (!hash) {
            console.log('No hash fragment found.');
            return {};
        }
        const [tabId, itemId] = hash.split(','); // Split by comma
        console.log(`Parsed fragment: Tab/Accordion ID = ${tabId || 'None'}, Item ID = ${itemId || 'None'}`);
        return { tabId, itemId };
    };

    // Function to scroll to a specific element
    const scrollToElement = (elementId, fallbackId) => {
    if (isScrolling || lastScrolledId === elementId) {
        console.log(`Skipping scroll: already scrolling or target ID (${elementId}) is the same as last scrolled ID.`);
        return;
    }

    const targetElement = document.getElementById(elementId) || document.getElementById(fallbackId);
    if (targetElement) {
        isScrolling = true; // Set scrolling flag
        lastScrolledId = elementId; // Update last scrolled ID

        console.log(`Scrolling to element: ID = ${targetElement.id}`);

        // Get the Y position considering the custom offset
        const yPosition = targetElement.offsetTop - customOffset;

        // Perform scrolling
        window.scrollTo({
            top: yPosition,
            behavior: 'smooth',
        });

        console.log(`Scroll position adjusted with offset. Target Y = ${yPosition}`);
        
        // Reset scrolling flag after the scroll has completed
        setTimeout(() => {
            isScrolling = false;
        }, 500); // Delay to allow smooth scrolling to finish
    } else {
        console.log(`Element not found: ID = ${elementId}.`);
    }
};

    // Function to debounce scrolls
    const debounceScroll = (func, delay = 300) => {
        clearTimeout(debounceTimer);
        debounceTimer = setTimeout(func, delay);
    };

    // Function to observe for IDs matching the hash fragment
    const observeForId = (itemId, fallbackId) => {
        const observer = new MutationObserver(() => {
            const targetElement = document.getElementById(itemId);
            if (targetElement) {
                console.log(`Target ID found: ${itemId}. Triggering scroll.`);
                observer.disconnect(); // Stop observing
                debounceScroll(() => scrollToElement(itemId, fallbackId));
            }
        });

        observer.observe(document.body, { childList: true, subtree: true });
        console.log(`Started observing DOM for ID = ${itemId}`);
    };

    // Function to handle accordion activation (mobile)
    const activateAccordionAndScroll = (accordionId, itemId) => {
        const accordionButton = document.querySelector(`[data-bs-target="#${accordionId}"]`);
        const accordionContent = document.getElementById(accordionId);
        if (accordionButton && accordionContent) {
            console.log(`Activating accordion: ID = ${accordionId}`);
            accordionButton.setAttribute('aria-expanded', 'true');
            accordionContent.classList.add('show'); // Ensure the accordion content is visible

            observeForId(itemId, accordionId);
        } else {
            console.log(`Accordion button or content not found for ID: ${accordionId}`);
            debounceScroll(() => scrollToElement(accordionId)); // Fallback to accordion section
        }
    };

    // Function to handle tab activation (desktop)
    const activateTabAndScroll = () => {
        const { tabId, itemId } = parseFragment();
        if (!tabId) {
            console.log('No tab/accordion ID found.');
            return;
        }

        const tabElement = document.querySelector(`a[href="#${tabId}"]`);
        if (tabElement) {
            console.log(`Activating tab: ID = ${tabId}`);
            const tab = new bootstrap.Tab(tabElement);
            tab.show();
            tabElement.addEventListener('shown.bs.tab', () => {
                console.log(`Tab activated: ID = ${tabId}`);
                debounceScroll(() => scrollToElement(itemId, tabId)); // Scroll to the specific item or fallback to tab
            }, { once: true });

            observeForId(itemId, tabId);
        } else {
            console.log(`No tab found for ID = ${tabId}. Checking for accordion.`);
            activateAccordionAndScroll(tabId, itemId);
        }
    };

    // Trigger the tab/accordion and scroll logic on page load or hash change
    const initialize = () => {
        console.log('Page loaded. Triggering tab/accordion and scroll logic immediately...');
        activateTabAndScroll();

        window.addEventListener('hashchange', () => {
            console.log('Hash changed. Re-evaluating fragment...');
            activateTabAndScroll();
        });
    };

    // Start observing for price updates
    const waitForPriceUpdate = () => {
        document.addEventListener(priceUpdateEventName, () => {
            console.log(`Custom event '${priceUpdateEventName}' detected.`);
            activateTabAndScroll();
        });
    };

    // Initialize the script
    initialize();
    waitForPriceUpdate();
});
 } catch { 
 console.log('JS error in Wyld Custom Code Module'); 
} 

 try { 
document.addEventListener('DOMContentLoaded', () => {

    const path = window.location.pathname;
	console.log('>> ' + path);
    if (
		path === "/holidays"
    ) {
		document.querySelector('.mobile-bar').remove();
		console.log('>> HIDING MENU');
    }

    });
 } catch { 
 console.log('JS error in Wyld Custom Code Module'); 
} 

	</script>
	<meta name="twitter:card" content="summary" />
	<meta name="twitter:site" content="EverTrek" />
	<link href="https://evertrek.co.uk" rel="canonical" />

    <link rel="stylesheet" href="/templates/zenbase/css/overrides.css?v=6825dfb03170f" />
    <meta name="robots" content="index, follow" />
          <!-- Google Tag Manager -->
      <script>
        (function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-5JRZBZT');      </script>
    <!-- End Google Tag Manager -->
    <!-- Adding GA4 Initialization -->
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
    </script>
    <!-- End GA4 Initialization -->
        <!-- mmenu CSS -->
    <link rel="stylesheet" href="/templates/zenbase/mmenu/dist/mmenu.css" />
    <link rel="stylesheet" href="/templates/zenbase/css/mmenu-custom.css" />
    <link rel="stylesheet" href="/templates/zenbase/css/menu-icons.css" />

    <script>
      jQuery(function() {
        // Video modal handling
        const videoModal = document.getElementById('videoLightbox');
        if (videoModal) {
          const videoIframe = videoModal.querySelector('iframe');
          let videoTime = 0;

          // Function to detect mobile devices
          function isMobileDevice() {
            return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
          }

          videoModal.addEventListener('hidden.bs.modal', function () {
            // Store current time
            if (videoIframe.contentWindow) {
              try {
                videoTime = videoIframe.contentWindow.postMessage('{"event":"command","func":"getCurrentTime","args":""}', '*');
              } catch (e) {
                console.log('Could not get video time');
              }
            }
            // Stop video
            const videoSrc = videoIframe.src;
            videoIframe.src = videoSrc;
          });

          videoModal.addEventListener('show.bs.modal', function () {
            // Add autoplay parameter for mobile devices only
            if (isMobileDevice() && videoIframe) {
              let currentSrc = videoIframe.src;
              // Check if URL already has parameters
              const separator = currentSrc.includes('?') ? '&' : '?';
              // Add autoplay=1 if not already present
              if (!currentSrc.includes('autoplay=')) {
                currentSrc += separator + 'autoplay=1';
                videoIframe.src = currentSrc;
              }
            }

            // Restore video time if it exists
            if (videoTime > 0) {
              try {
                videoIframe.contentWindow.postMessage('{"event":"command","func":"seekTo","args":[' + videoTime + ',true]}', '*');
              } catch (e) {
                console.log('Could not seek to time');
              }
            }
          });
        }
      });
    </script>
  </head>
  <body class="homepage p-0">
          <!-- Google Tag Manager (noscript) -->
      <noscript><iframe class="in-viewport" data-vp_path="https://www.googletagmanager.com/ns.html?id=GTM-5JRZBZT"
      height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript>
      <!-- End Google Tag Manager (noscript) -->
    
    <!-- Mobile Menu -->
    <div id="mobile-menu" class="mm-menu">
              <!-- Menu Debug: Initialization
Total menu items: 90
First level items: 6
Second level items: 3
Third level items: 11
-->
<ul class="mm-listview"><li class="has-submenu parent no-click"><!-- Processing title: Destinations -->
<span title="" class="" rel="">Destinations</span><ul class="mm-listview"><li class="has-submenu parent"><span>Asia</span><ul class="mm-listview"><li><!-- Processing title: Nepal -->
<span>Nepal</span><!-- Found 13 trips for country ID 116 -->
<ul class="mm-listview"><li><a href="/holidays/everest-base-camp-trek"><!-- Processing title: Everest Base Camp Trek -->
Everest Base Camp Trek</a></li><li><a href="/holidays/everest-three-passes-trek"><!-- Processing title: Everest Three Passes Trek -->
Everest Three Passes Trek</a></li><li><a href="/holidays/everest-base-camp-via-gokyo-valley"><!-- Processing title: Everest Base Camp Via Gokyo Valley -->
Everest Base Camp Via Gokyo Valley</a></li><li><a href="/holidays/ultimate-island-peak-and-everest-base-camp-expedition"><!-- Processing title: Ultimate Island Peak and Everest Base Camp Expedition -->
Ultimate Island Peak and Everest Base Camp Expedition</a></li><li><a href="/holidays/annapurna-base-camp-trek"><!-- Processing title: Annapurna Base Camp Trek -->
Annapurna Base Camp Trek</a></li><li><a href="/holidays/langtang-valley-trek"><!-- Processing title: Langtang Valley Trek -->
Langtang Valley Trek</a></li><li><a href="/holidays/ultimate-mera-peak-expedition"><!-- Processing title: Ultimate Mera Peak Expedition -->
Ultimate Mera Peak Expedition</a></li><li><a href="/holidays/island-peak-expedition"><!-- Processing title: Island Peak Expedition -->
Island Peak Expedition</a></li><li><a href="/holidays/annapurna-circuit-trek"><!-- Processing title: Annapurna Circuit Trek -->
Annapurna Circuit Trek</a></li></ul></li><li><!-- Processing title: Pakistan -->
<span>Pakistan</span><!-- Found 1 trips for country ID 454 -->
<ul class="mm-listview"><li><a href="/holidays/k2-base-camp-trek"><!-- Processing title: K2 Base Camp Trek -->
K2 Base Camp Trek</a></li></ul></li></ul></li><li class="has-submenu parent"><span>South America</span><ul class="mm-listview"><li><!-- Processing title: Peru -->
<span>Peru</span><!-- Found 3 trips for country ID 141 -->
<ul class="mm-listview"><li><a href="/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley"><!-- Processing title: Machu Picchu via Tomacaya route - The Hidden Valley -->
Machu Picchu via Tomacaya route - The Hidden Valley</a></li><li><a href="/holidays/machu-picchu-via-inca-trail"><!-- Processing title: Machu Picchu via Inca Trail - The Way of the Incas -->
Machu Picchu via Inca Trail - The Way of the Incas</a></li></ul></li></ul></li><li class="has-submenu parent"><span>Africa</span><ul class="mm-listview"><li><!-- Processing title: Tanzania -->
<span>Tanzania</span><!-- Found 2 trips for country ID 117 -->
<ul class="mm-listview"><li><a href="/holidays/kilimanjaro-the-long-way"><!-- Processing title: Kilimanjaro The Long Way -->
Kilimanjaro The Long Way</a></li></ul></li><li><!-- Processing title: Morocco -->
<span>Morocco</span><!-- Found 3 trips for country ID 118 -->
<ul class="mm-listview"><li><a href="/holidays/mt-toubkal-roof-of-the-north-weekender"><!-- Processing title: Toubkal Roof Of The North Weekender -->
Toubkal Roof Of The North Weekender</a></li><li><a href="/holidays/mt-toubkal-roof-of-the-north-8-day-trek"><!-- Processing title: Toubkal Roof Of The North 8 Day Trek -->
Toubkal Roof Of The North 8 Day Trek</a></li></ul></li></ul></li><li class="has-submenu parent"><span>Europe</span><ul class="mm-listview"><li><!-- Processing title: France -->
<span>France</span><!-- Found 1 trips for country ID 659 -->
<ul class="mm-listview"><li><a href="/holidays/tour-du-mont-blanc-trek"><!-- Processing title: Tour du Mont Blanc Trek -->
Tour du Mont Blanc Trek</a></li></ul></li><li><!-- Processing title: Greece -->
<span>Greece</span><!-- Found 1 trips for country ID 764 -->
<ul class="mm-listview"><li><a href="/holidays/mt-olympus-myths-and-legends-weekender"><!-- Processing title: Mount Olympus Weekender -->
Mount Olympus Weekender</a></li></ul></li><li><!-- Processing title: Italy -->
<span>Italy</span><!-- Found 1 trips for country ID 470 -->
<ul class="mm-listview"><li><a href="/holidays/tour-du-mont-blanc-trek"><!-- Processing title: Tour du Mont Blanc Trek -->
Tour du Mont Blanc Trek</a></li></ul></li><li><!-- Processing title: United Kingdom -->
<span>United Kingdom</span><!-- Found 3 trips for country ID 504 -->
<ul class="mm-listview"><li><a href="/holidays/uk-training-weekend"><!-- Processing title: Brecon Training Weekend -->
Brecon Training Weekend</a></li><li><a href="https://evertrek.co.uk/holidays/north-wales-training-weekend"><!-- Processing title: Snowdonia Training Weekend -->
Snowdonia Training Weekend</a></li><li><a href="/holidays/scotland-winter-skills-weekend"><!-- Processing title: Scotland Winter Skills -->
Scotland Winter Skills</a></li></ul></li></ul></li></ul></li><li class="has-submenu parent no-click"><!-- Processing title: Collections -->
<span title="" class="" rel="">Collections</span><ul class="mm-listview"><li class="has-submenu parent no-click"><!-- Processing title: Best Sellers -->
<span><span class="zen-menu__icon"><svg fill="currentColor" width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1475_17087)">
<path d="M14.0951 9.90394L13.4527 7.94483L15.1343 6.73387C15.4622 6.49784 15.593 6.09847 15.4676 5.71656C15.3424 5.33465 14.9996 5.08784 14.5945 5.08784H12.5161L11.8739 3.12872C11.7484 2.74682 11.4058 2.5 11.0005 2.5C10.5952 2.5 10.2526 2.74682 10.1271 3.12872L9.48494 5.08784H7.40649C7.00116 5.08784 6.65856 5.33465 6.53312 5.71656C6.40795 6.09873 6.53883 6.49784 6.86666 6.73413L8.54808 7.94483L7.90589 9.90369C7.78071 10.2856 7.91159 10.685 8.23942 10.9213C8.40347 11.0391 8.59136 11.0982 8.77925 11.0982C8.96714 11.0982 9.15503 11.0391 9.31908 10.9213L11.0005 9.71055L12.6819 10.9213C13.0098 11.157 13.4332 11.157 13.7611 10.9213C14.0891 10.6852 14.2203 10.2859 14.0951 9.90394ZM10.8844 3.37271C10.9069 3.30363 10.9601 3.28899 11.0005 3.28899C11.0409 3.28899 11.0941 3.30363 11.1166 3.37271L11.8482 5.60433C11.9016 5.7669 12.0545 5.87682 12.2268 5.87682H14.5945C14.6679 5.87682 14.6979 5.92228 14.7106 5.96029C14.7231 5.99856 14.7256 6.05301 14.6663 6.09564L12.7506 7.47483C12.6112 7.57525 12.5529 7.75349 12.606 7.91607L13.3378 10.1474C13.3604 10.2165 13.3262 10.2594 13.2933 10.2828C13.2609 10.3067 13.2098 10.3257 13.15 10.2828L11.2345 8.90359C11.0951 8.80342 10.9059 8.80342 10.7665 8.90359L8.85104 10.2828C8.79117 10.3254 8.74038 10.3061 8.70746 10.2828C8.67481 10.2591 8.64034 10.2168 8.66315 10.1477L9.39475 7.91607C9.44788 7.75349 9.38957 7.57525 9.25014 7.47483L7.3347 6.09564C7.27535 6.05301 7.27795 5.99856 7.29039 5.96055C7.30283 5.92228 7.33315 5.87682 7.40649 5.87682H9.77416C9.9465 5.87682 10.0994 5.7669 10.1528 5.60433" />
<path d="M7.81093 11.6627H5.84599L5.23904 9.81073C5.1136 9.42882 4.771 9.18201 4.36567 9.18201C3.96035 9.18201 3.61774 9.42882 3.49231 9.81073L2.8851 11.6627H0.920161C0.514837 11.6627 0.172229 11.9096 0.0467965 12.2915C-0.078377 12.6736 0.052498 13.0728 0.380334 13.309L1.97001 14.4535L1.3628 16.3057C1.23763 16.6877 1.36876 17.087 1.69686 17.3231C2.02469 17.5591 2.44816 17.5586 2.77599 17.3231L4.36567 16.1784L5.95535 17.3231C6.1194 17.4409 6.30729 17.5 6.49518 17.5C6.68307 17.5 6.87096 17.4409 7.03475 17.3231C7.36258 17.087 7.49346 16.6877 7.36828 16.3057L6.76133 14.4535L8.35075 13.309C8.67859 13.0728 8.80946 12.6734 8.68429 12.2915C8.55912 11.9096 8.21625 11.6627 7.81093 11.6627ZM7.88271 12.6706L6.05927 13.9835C5.91985 14.0839 5.86154 14.2621 5.91466 14.4245L6.61102 16.5495C6.63383 16.6186 6.59936 16.6612 6.56671 16.6848C6.53457 16.7085 6.483 16.7277 6.42339 16.6846L4.59969 15.3714C4.52998 15.3213 4.44783 15.2961 4.36567 15.2961C4.28352 15.2961 4.20137 15.3213 4.13165 15.3714L2.30795 16.6846C2.24835 16.7275 2.19678 16.7082 2.16464 16.6846C2.13173 16.6612 2.09752 16.6183 2.12006 16.5495L2.81668 14.4247C2.86981 14.2621 2.8115 14.0839 2.67207 13.9835L0.848374 12.6706C0.789026 12.6279 0.791618 12.5735 0.804058 12.5355C0.816497 12.4972 0.846819 12.4517 0.920161 12.4517H3.17432C3.34666 12.4517 3.49957 12.3418 3.55295 12.1792L4.24957 10.0547C4.27212 9.98563 4.32524 9.97099 4.36567 9.97099C4.4061 9.97099 4.45923 9.98563 4.48178 10.0547L5.17813 12.1792C5.23152 12.3416 5.38442 12.4517 5.55677 12.4517H7.81093C7.88427 12.4517 7.91459 12.4972 7.92703 12.5352C7.93973 12.5735 7.94206 12.6279 7.88271 12.6706Z" />
<path d="M21.9528 12.2915C21.8277 11.9096 21.4848 11.6627 21.0795 11.6627H19.1145L18.5076 9.81073C18.3822 9.42882 18.0396 9.18201 17.6342 9.18201C17.2289 9.18201 16.8863 9.42882 16.7609 9.81073L16.1537 11.6627H14.1887C13.7834 11.6627 13.4408 11.9096 13.3154 12.2915C13.1902 12.6736 13.3211 13.0728 13.6489 13.309L15.2386 14.4535L14.6314 16.3057C14.5062 16.6877 14.6373 17.087 14.9654 17.3231C15.2932 17.5591 15.7167 17.5586 16.0445 17.3231L17.6342 16.1784L19.2239 17.3231C19.388 17.4409 19.5758 17.5 19.7637 17.5C19.9516 17.5 20.1395 17.4409 20.3033 17.3231C20.6311 17.087 20.762 16.6877 20.6368 16.3057L20.0299 14.4535L21.6193 13.309C21.9471 13.0728 22.078 12.6734 21.9528 12.2915ZM21.1513 12.6706L19.3278 13.9835C19.1884 14.0839 19.1301 14.2621 19.1832 14.4245L19.8796 16.5495C19.9024 16.6186 19.8679 16.6612 19.8353 16.6848C19.8031 16.7085 19.7516 16.7277 19.6919 16.6846L17.8682 15.3714C17.7985 15.3213 17.7164 15.2961 17.6342 15.2961C17.5521 15.2961 17.4699 15.3213 17.4002 15.3714L15.5765 16.6846C15.5169 16.7275 15.4653 16.7082 15.4332 16.6846C15.4003 16.6612 15.3661 16.6183 15.3886 16.5495L16.0852 14.4247C16.1384 14.2621 16.0801 14.0839 15.9406 13.9835L14.1169 12.6706C14.0576 12.6279 14.0602 12.5735 14.0726 12.5355C14.0851 12.4972 14.1154 12.4517 14.1887 12.4517H16.4429C16.6152 12.4517 16.7681 12.3418 16.8215 12.1792L17.5181 10.0547C17.5407 9.98563 17.5938 9.97099 17.6342 9.97099C17.6747 9.97099 17.7278 9.98563 17.7503 10.0547L18.4467 12.1792C18.5001 12.3416 18.653 12.4517 18.8253 12.4517H21.0795C21.1528 12.4517 21.1831 12.4972 21.1956 12.5352C21.2083 12.5735 21.2106 12.6279 21.1513 12.6706Z" />
</g>
<defs>
<clipPath id="clip0_1475_17087">
<rect width="22" height="15"  transform="translate(0 2.5)"/>
</clipPath>
</defs>
</svg>
</span>Best Sellers</span><ul class="mm-listview"><li><!-- Processing title: Everest Base Camp Trek -->
<a href="https://evertrek.co.uk/holidays/everest-base-camp-trek">Everest Base Camp Trek</a></li><li><!-- Processing title: Kilimanjaro The Long Way -->
<a href="https://evertrek.co.uk/holidays/kilimanjaro-the-long-way">Kilimanjaro The Long Way</a></li><li><!-- Processing title: Machu Picchu via Tomacaya -->
<a href="https://evertrek.co.uk/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley">Machu Picchu via Tomacaya</a></li><li><!-- Processing title: Toubkal Roof of the North Weekender -->
<a href="https://evertrek.co.uk/holidays/mt-toubkal-roof-of-the-north-weekender">Toubkal Roof of the North Weekender</a></li><li><!-- Processing title: Tour du Mont Blanc Trek -->
<a href="https://evertrek.co.uk/holidays/tour-du-mont-blanc-trek">Tour du Mont Blanc Trek</a></li><li><!-- Processing title: Everest Base Camp via Gokyo Valley -->
<a href="https://evertrek.co.uk/holidays/everest-base-camp-via-gokyo-valley">Everest Base Camp via Gokyo Valley</a></li><li><!-- Processing title: Ultimate Island Peak and Everest Base Camp -->
<a href="https://evertrek.co.uk/holidays/ultimate-island-peak-and-everest-base-camp-expedition">Ultimate Island Peak and Everest Base Camp</a></li></ul></li><li class="has-submenu parent no-click"><!-- Processing title: Bucket List Adventures -->
<span><span class="zen-menu__icon"><svg fill="currentColor" width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1060_2276)">
<path d="M26.9978 4.62856C26.4867 4.24684 25.7714 4.34573 25.371 4.85483L24.792 5.59012C23.0895 4.58126 21.1107 4 18.9991 4C12.6584 4 7.5 9.21796 7.5 15.6317C7.5 18.7066 8.68779 21.5046 10.6209 23.5866L10.0403 24.324C9.64046 24.8319 9.704 25.5578 10.1844 25.9767C12.1227 27.6681 14.4459 28.741 16.9241 29.1263V31.8022H13.2859C12.1419 31.8022 11.2109 32.7437 11.2109 33.9011C11.2109 35.0585 12.1419 36 13.2859 36H24.712C25.8563 36 26.7874 35.0585 26.7874 33.9011C26.7874 32.7437 25.8563 31.8022 24.712 31.8022H21.074V29.1266C27.3273 28.1564 32.1575 22.8304 32.4824 16.3426C32.7135 11.7377 30.6632 7.35878 26.9978 4.62856ZM31.2826 16.2809C30.9898 22.1277 26.6814 26.9412 21.074 27.8988C20.8755 27.9328 20.6751 27.9611 20.4734 27.9852C20.3078 28.005 20.1416 28.0229 19.974 28.036C19.9401 28.0386 19.9065 28.039 19.8727 28.0414C19.2857 28.0824 18.702 28.084 18.1254 28.0433C17.9243 28.0291 17.724 28.0101 17.5247 27.9861C17.3234 27.962 17.1232 27.9329 16.9241 27.8989C14.7367 27.5254 12.6893 26.5621 10.98 25.0811L11.4917 24.4313C13.0096 25.7582 14.8721 26.6901 16.9241 27.0701C17.1226 27.1069 17.3228 27.1383 17.5247 27.1646C17.7235 27.1904 17.9237 27.2111 18.1254 27.2265C18.4142 27.2485 18.7048 27.2635 18.9991 27.2635C19.2933 27.2635 19.584 27.2485 19.8727 27.2265C20.0744 27.2111 20.2747 27.1904 20.4734 27.1646C20.6753 27.1383 20.8755 27.1069 21.074 27.0701C26.4271 26.0787 30.4981 21.3287 30.4981 15.6317C30.4981 11.7904 28.6466 8.37943 25.8001 6.26009L26.3036 5.62065C29.6319 8.10918 31.4926 12.0922 31.2826 16.2809ZM25.586 33.9011C25.586 34.3885 25.1938 34.7848 24.712 34.7848H13.2859C12.8041 34.7848 12.4123 34.3885 12.4123 33.9011C12.4123 33.4138 12.8041 33.0174 13.2859 33.0174H24.712C25.1938 33.0174 25.586 33.4138 25.586 33.9011ZM18.1254 31.8022V29.2579C18.4147 29.2765 18.7049 29.2892 18.9969 29.2892C19.2874 29.2892 19.58 29.2736 19.8727 29.2548V31.8022H18.1254ZM18.9991 5.21519C20.8277 5.21519 22.5452 5.70201 24.0353 6.551C24.2115 6.6514 24.3843 6.75719 24.5539 6.86758C24.7229 6.9776 24.8889 7.09187 25.051 7.21143C27.6218 9.1072 29.2968 12.1746 29.2968 15.6317C29.2968 20.6566 25.7611 24.8619 21.074 25.8357C20.876 25.8769 20.6754 25.9104 20.4734 25.9398C20.275 25.9688 20.0745 25.9909 19.8727 26.0081C19.5844 26.0328 19.2936 26.0483 18.9991 26.0483C18.7046 26.0483 18.4137 26.0328 18.1254 26.0081C17.9236 25.9909 17.7231 25.9688 17.5247 25.9398C17.3227 25.9104 17.1221 25.8769 16.9241 25.8357C15.1556 25.4683 13.5529 24.6386 12.2403 23.4806C12.0889 23.3471 11.9409 23.2099 11.7975 23.0678C11.6533 22.9248 11.5118 22.7789 11.376 22.6276C9.71495 20.7773 8.70133 18.322 8.70133 15.6317C8.70133 9.88805 13.3209 5.21519 18.9991 5.21519Z" />
<path d="M14.051 15.6495L15.7912 16.9284L15.7413 17.0837L15.212 18.7312L15.1264 18.9976C15.0358 19.2798 15.0364 19.5675 15.1137 19.8312C15.1976 20.1173 15.3723 20.3751 15.6295 20.5645C16.0185 20.8507 16.4963 20.9081 16.9239 20.7442C17.0399 20.6998 17.153 20.6423 17.2587 20.5649L17.5245 20.3695L18.1253 19.928L18.9989 19.286L19.8725 19.9279L20.4733 20.3692L20.7395 20.5649C20.845 20.6424 20.958 20.6998 21.0739 20.7443C21.2295 20.804 21.391 20.8374 21.5533 20.8374C21.8372 20.8374 22.1207 20.7464 22.3683 20.5645C22.8626 20.2009 23.06 19.5858 22.8712 18.9976L22.2068 16.9284L23.947 15.6495C24.4416 15.2856 24.6391 14.6709 24.4503 14.0823C24.261 13.494 23.744 13.1139 23.1324 13.1139H20.9812L20.8138 12.5924L20.5914 11.8996L20.369 11.2069L20.3168 11.0443C20.1275 10.4561 19.6105 10.0759 18.9989 10.0759C18.3875 10.0759 17.8701 10.4561 17.681 11.0443L17.0164 13.1139H14.8654C14.254 13.1139 13.7366 13.494 13.5477 14.0823C13.3587 14.6705 13.5564 15.2856 14.051 15.6495ZM18.0242 13.9094L18.8237 11.4201C18.8577 11.3137 18.9379 11.2911 18.9989 11.2911C19.0599 11.2911 19.1401 11.3137 19.1741 11.4201L19.4716 12.3465L19.6941 13.0392L19.9165 13.7318L19.9734 13.909C20.054 14.1594 20.2847 14.3291 20.5448 14.3291H23.1324C23.2431 14.3291 23.2888 14.3991 23.3076 14.458C23.3264 14.5166 23.3303 14.6005 23.2407 14.6661L21.1474 16.2045C20.937 16.3592 20.849 16.6337 20.9292 16.8837L21.7285 19.3734C21.7629 19.4798 21.7113 19.5451 21.6616 19.5815C21.6127 19.6179 21.5341 19.6475 21.4454 19.5815L21.0739 19.3084L20.4733 18.8671L19.352 18.0431C19.2466 17.966 19.1229 17.9272 18.9989 17.9272C18.8749 17.9272 18.7512 17.966 18.6458 18.0431L17.5245 18.8672L16.9239 19.3086L16.5526 19.5815C16.4633 19.6471 16.3853 19.6179 16.3358 19.5815C16.2865 19.5451 16.2349 19.4794 16.2691 19.3734L16.8167 17.6685L17.0686 16.8841C17.1187 16.7281 17.0968 16.5658 17.0273 16.4259C16.9852 16.3413 16.9298 16.2628 16.8504 16.2045L16.5865 16.0105L14.7573 14.6661C14.6677 14.6005 14.6715 14.5166 14.6902 14.4577C14.7092 14.3991 14.7547 14.3291 14.8654 14.3291H17.4529C17.7131 14.3291 17.9436 14.1594 18.0242 13.9094Z" />
</g>
<defs>
<clipPath id="clip0_1060_2276">
<rect width="25" height="32" fill="white" transform="translate(7.5 4)"/>
</clipPath>
</defs>
</svg>
</span>Bucket List Adventures</span><ul class="mm-listview"><li><!-- Processing title: Kilimanjaro The Long Way -->
<a href="https://evertrek.co.uk/holidays/kilimanjaro-the-long-way">Kilimanjaro The Long Way</a></li><li><!-- Processing title: Everest Base Camp Trek -->
<a href="https://evertrek.co.uk/holidays/everest-base-camp-trek">Everest Base Camp Trek</a></li><li><!-- Processing title: Machu Picchu via Inca Trail -->
<a href="https://evertrek.co.uk/holidays/machu-picchu-via-inca-trail">Machu Picchu via Inca Trail</a></li><li><!-- Processing title: Tour du Mont Blanc Trek -->
<a href="https://evertrek.co.uk/holidays/tour-du-mont-blanc-trek">Tour du Mont Blanc Trek</a></li><li><!-- Processing title: K2 Base Camp Trek -->
<a href="https://evertrek.co.uk/holidays/k2-base-camp-trek">K2 Base Camp Trek</a></li></ul></li><li class="has-submenu parent no-click"><!-- Processing title: Short Trips -->
<span><span class="zen-menu__icon"><?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg fill="currentColor" width="100%" height="100%" viewBox="0 0 41 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <path d="M6.899,21.719C6.883,21.63 6.874,21.539 6.874,21.446L6.874,18.594C6.874,17.756 7.559,17.071 8.393,17.071L9.973,17.071C10.171,17.071 10.361,17.11 10.536,17.18L10.536,11.517C10.139,10.924 9.906,10.212 9.906,9.447C9.906,7.397 11.577,5.721 13.619,5.721L15.718,5.721C15.49,5.379 15.356,4.968 15.356,4.528C15.356,3.343 16.322,2.375 17.502,2.375L23.231,2.375C24.41,2.375 25.377,3.343 25.377,4.528C25.377,4.968 25.243,5.379 25.014,5.721L27.098,5.721C29.139,5.721 30.81,7.397 30.81,9.447C30.81,10.212 30.578,10.924 30.181,11.517L30.181,17.367C30.433,17.181 30.743,17.071 31.077,17.071L32.658,17.071C33.492,17.071 34.176,17.756 34.176,18.594L34.176,21.446C34.176,21.54 34.167,21.632 34.151,21.722C34.943,21.77 35.577,22.435 35.577,23.241L35.577,31.046C35.577,31.884 34.892,32.568 34.058,32.567L30.59,32.567C30.582,32.567 30.573,32.567 30.564,32.567C30.723,32.981 30.81,33.43 30.81,33.899C30.81,35.949 29.139,37.625 27.098,37.625L13.619,37.625C11.577,37.625 9.906,35.949 9.906,33.899C9.906,33.43 9.993,32.981 10.152,32.567L6.799,32.567C5.965,32.567 5.281,31.883 5.281,31.045L5.281,23.241C5.281,22.403 5.965,21.719 6.799,21.719L6.899,21.719ZM10.536,31.046L10.536,23.241C10.536,23.092 10.416,22.969 10.267,22.969L9.973,22.968L8.393,22.968L6.799,22.969C6.651,22.969 6.531,23.092 6.531,23.241L6.531,31.045C6.531,31.194 6.651,31.318 6.799,31.318L10.268,31.318C10.416,31.318 10.536,31.194 10.536,31.046ZM28.931,30.661L28.931,12.685C28.389,12.996 27.763,13.174 27.098,13.174L25.847,13.174L25.848,13.849L25.825,14.069L25.761,14.276L25.66,14.462L25.527,14.623L25.366,14.757L25.18,14.859L24.973,14.924L24.752,14.947L24.53,14.924L24.324,14.859L24.137,14.757L23.976,14.624L23.843,14.462L23.742,14.276L23.678,14.069L23.656,13.849L23.656,13.174L17.548,13.174L17.548,13.849L17.526,14.069L17.462,14.276L17.361,14.462L17.228,14.624L17.067,14.757L16.881,14.859L16.674,14.924L16.452,14.947L16.231,14.924L16.024,14.859L15.838,14.757L15.677,14.623L15.544,14.462L15.443,14.276L15.379,14.069L15.357,13.849L15.357,13.174L13.619,13.174C12.954,13.174 12.328,12.996 11.786,12.685L11.786,30.661C12.328,30.35 12.954,30.172 13.619,30.172L27.098,30.172C27.763,30.172 28.389,30.35 28.931,30.661ZM32.658,22.968L31.077,22.968L30.59,22.969C30.442,22.969 30.322,23.092 30.322,23.241L30.322,31.045C30.322,31.194 30.442,31.318 30.59,31.318L34.059,31.318C34.207,31.318 34.327,31.194 34.327,31.046L34.327,23.241C34.327,23.092 34.207,22.969 34.058,22.969L32.658,22.968ZM31.077,21.718L32.658,21.718C32.806,21.718 32.926,21.595 32.926,21.446L32.926,18.594C32.926,18.445 32.806,18.321 32.658,18.321L31.077,18.321C30.929,18.321 30.809,18.445 30.809,18.594L30.809,21.446C30.809,21.595 30.929,21.718 31.077,21.718ZM11.732,11.034L11.786,11.034L11.786,11.097C12.237,11.604 12.893,11.924 13.619,11.924L15.357,11.924L15.357,11.118L15.379,10.898L15.443,10.691L15.544,10.505L15.677,10.343L15.838,10.21L16.024,10.108L16.231,10.043L16.452,10.021L16.674,10.043L16.881,10.108L17.067,10.21L17.228,10.343L17.361,10.505L17.462,10.691L17.526,10.898L17.548,11.118L17.548,11.924L23.656,11.924L23.656,11.118L23.678,10.898L23.742,10.691L23.843,10.505L23.976,10.343L24.137,10.21L24.324,10.108L24.53,10.043L24.752,10.021L24.973,10.043L25.18,10.108L25.366,10.21L25.527,10.343L25.66,10.505L25.761,10.691L25.825,10.898L25.848,11.118L25.848,11.924L27.098,11.924C27.824,11.924 28.479,11.604 28.931,11.097L28.931,11.034L28.985,11.034C29.344,10.603 29.56,10.049 29.56,9.447C29.56,8.086 28.453,6.971 27.098,6.971L13.619,6.971C12.264,6.971 11.156,8.086 11.156,9.447C11.156,10.049 11.373,10.603 11.732,11.034ZM8.393,21.718L9.974,21.718C10.122,21.718 10.241,21.595 10.241,21.446L10.242,18.594C10.242,18.445 10.121,18.321 9.973,18.321L8.393,18.321C8.244,18.321 8.124,18.445 8.124,18.594L8.124,21.446C8.124,21.595 8.244,21.718 8.393,21.718ZM29.56,33.899C29.56,32.537 28.453,31.422 27.098,31.422L13.619,31.422C12.264,31.422 11.156,32.537 11.156,33.899C11.156,35.26 12.264,36.375 13.619,36.375L27.098,36.375C28.453,36.375 29.56,35.26 29.56,33.899ZM24.127,4.528C24.127,4.032 23.724,3.625 23.231,3.625L17.502,3.625C17.008,3.625 16.606,4.032 16.606,4.528C16.606,5.023 17.008,5.43 17.502,5.43L23.23,5.43C23.724,5.43 24.127,5.023 24.127,4.528ZM26.954,25.303C26.954,26.658 25.849,27.765 24.502,27.764L16.284,27.764C14.936,27.764 13.832,26.658 13.832,25.303L13.832,21.348C13.832,19.994 14.936,18.887 16.284,18.887L24.502,18.887C25.85,18.887 26.954,19.994 26.954,21.348L26.954,25.303ZM25.704,25.303L25.704,21.348C25.704,20.683 25.164,20.137 24.502,20.137L16.284,20.137C15.622,20.137 15.082,20.683 15.082,21.348L15.082,25.303C15.082,25.969 15.622,26.514 16.284,26.514L24.502,26.514C25.164,26.514 25.704,25.968 25.704,25.303Z"/>
</svg>
</span>Short Trips</span><ul class="mm-listview"><li><!-- Processing title: Toubkal Roof of the North Weekender -->
<a href="https://evertrek.co.uk/holidays/mt-toubkal-roof-of-the-north-weekender">Toubkal Roof of the North Weekender</a></li><li><!-- Processing title: Mount Olympus Weekender -->
<a href="https://evertrek.co.uk/holidays/mt-olympus-myths-and-legends-weekender">Mount Olympus Weekender</a></li><li><!-- Processing title: Brecon Beacons Training Weekend -->
<a href="https://evertrek.co.uk/holidays/uk-training-weekend">Brecon Beacons Training Weekend</a></li><li><!-- Processing title: Scottish Winter Skills -->
<a href="https://evertrek.co.uk/holidays/scotland-winter-skills-weekend">Scottish Winter Skills</a></li><li><!-- Processing title: Snowdonia Training Weekend -->
<a href="https://evertrek.co.uk/holidays/north-wales-training-weekend">Snowdonia Training Weekend</a></li></ul></li><li class="has-submenu parent no-click"><!-- Processing title: Training Weekends -->
<span><span class="zen-menu__icon"><svg fill="currentColor" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1475_17084)">
<path d="M18.3406 4.10164H17.931V4.00914C17.931 3.51611 17.5057 3.11493 16.983 3.11493H16.3528C15.8302 3.11493 15.4049 3.51611 15.4049 4.00914V5.82833H13.6769C13.3876 5.54694 13.0361 5.37007 12.7012 5.26854V4.54468C12.7012 4.06212 12.2852 3.6697 11.7736 3.6697H11.6255C11.5384 3.6697 11.4556 3.68476 11.3755 3.70607C11.2918 3.30421 10.9164 3 10.4661 3H10.3183C9.83033 3 9.43371 3.35821 9.39771 3.80994C9.25307 3.72195 9.08187 3.66948 8.89732 3.66948H8.74922C8.26123 3.66948 7.86455 4.02779 7.82866 4.47962C7.68402 4.39165 7.51282 4.33918 7.32827 4.33918H7.18017C6.66863 4.33918 6.25265 4.7316 6.25265 5.21417V5.82833H4.59506V4.00914C4.59506 3.51611 4.16979 3.11493 3.64715 3.11493H3.01695C2.49431 3.11493 2.06905 3.51611 2.06905 4.00914V4.10164H1.65941C1.02015 4.10164 0.5 4.59232 0.5 5.19537V8.84421C0.5 9.44726 1.02015 9.93794 1.65941 9.93794H2.06905V10.0304C2.06905 10.5235 2.49431 10.9247 3.01695 10.9247H3.64715C4.16979 10.9247 4.59506 10.5235 4.59506 10.0304V7.96452H6.12199V8.89313C6.12199 9.19455 6.20691 9.49169 6.36768 9.75209L7.10743 10.9515L7.51147 11.6065C7.62854 11.7964 7.69058 12.0133 7.69058 12.2335V16.6719C7.69058 16.853 7.84638 17 8.03841 17C8.23043 17 8.38623 16.853 8.38623 16.6719V12.2335C8.38623 11.8975 8.29157 11.5664 8.11268 11.2765L6.9689 9.42205C6.86994 9.26162 6.81763 9.07876 6.81763 8.89313V7.99279C6.92907 8.0376 7.05155 8.06258 7.18017 8.06258H7.32827C7.47925 8.06258 7.61988 8.02514 7.74595 7.96452H8.02389C8.19386 8.1677 8.45411 8.30076 8.74922 8.30076H8.89732C9.19243 8.30076 9.45268 8.1677 9.62265 7.96452H9.90059C10.0267 8.02514 10.1673 8.06258 10.3183 8.06258H10.337C9.9636 8.6096 9.99496 9.25269 9.99672 9.28384C10.0074 9.46413 10.1684 9.59829 10.3615 9.59295C10.3851 9.59181 10.408 9.58839 10.4301 9.58308C10.5868 9.54528 10.6992 9.40747 10.6915 9.24923C10.6912 9.24325 10.6663 8.64127 11.0427 8.27192C11.2512 8.06749 11.5637 7.97136 11.9559 7.96794C11.9699 7.96801 11.9828 7.96582 11.9961 7.96452C12.0773 7.95662 12.1468 7.92551 12.2034 7.87267C12.2693 7.81114 12.3062 7.72719 12.3062 7.63982V7.09082C12.3062 7.08396 12.3045 7.07752 12.3041 7.07077C12.2939 6.91412 12.1681 6.78816 12.0055 6.76717C11.99 6.76517 11.9746 6.7627 11.9584 6.7627H11.3121C11.2801 6.7625 11.1768 6.75868 11.0459 6.72497C10.9404 6.6978 10.8171 6.65054 10.698 6.57027C10.6611 6.54537 10.6249 6.51634 10.5896 6.48457C10.4978 6.40219 10.4141 6.2951 10.3491 6.15643C10.3048 6.06215 10.2689 5.95429 10.2461 5.82833C10.2454 5.82397 10.2441 5.82033 10.2433 5.81594L10.698 5.81344L11.0459 5.81153L11.3937 5.80962L11.9571 5.80654C11.9623 5.80658 11.9811 5.8072 12.0055 5.80853C12.0466 5.81073 12.11 5.81594 12.1918 5.82833C12.2404 5.8357 12.295 5.84631 12.3533 5.85962C12.4601 5.88396 12.5789 5.91883 12.7012 5.97129C12.8099 6.01794 12.9196 6.07961 13.0244 6.15643C13.1424 6.24304 13.2536 6.34948 13.3459 6.48457C13.4513 6.63876 13.5329 6.82773 13.5741 7.06006V8.89313C13.5741 9.07898 13.5218 9.26184 13.4229 9.42205L12.2793 11.2763C12.1002 11.5664 12.0055 11.8975 12.0055 12.2335V16.6719C12.0055 16.853 12.1613 17 12.3533 17C12.5454 17 12.7012 16.853 12.7012 16.6719V12.2335C12.7012 12.0133 12.7632 11.7964 12.8805 11.6065L14.0241 9.75231C14.1848 9.49191 14.2698 9.19476 14.2698 8.89313V7.96452H15.4049V10.0304C15.4049 10.5235 15.8302 10.9247 16.3528 10.9247H16.983C17.5057 10.9247 17.931 10.5235 17.931 10.0304V9.93794H18.3406C18.9799 9.93794 19.5 9.44726 19.5 8.84421V5.19537C19.5 4.59232 18.9799 4.10164 18.3406 4.10164ZM18.8044 8.84421C18.8044 9.08539 18.5963 9.2817 18.3406 9.2817H17.931V4.75788H18.3406C18.5963 4.75788 18.8044 4.95419 18.8044 5.19537V8.84421ZM14.2657 6.98401C14.2357 6.80091 14.1875 6.63503 14.1249 6.48457H15.4049V7.30828H14.2698V7.034C14.2698 7.01733 14.2684 7.00046 14.2657 6.98401ZM16.1006 10.0304V4.00914C16.1006 3.87798 16.2138 3.77117 16.3528 3.77117H16.983C17.1221 3.77117 17.2353 3.87798 17.2353 4.00914V10.0304C17.2353 10.1616 17.1221 10.2684 16.983 10.2684H16.3528C16.2138 10.2684 16.1006 10.1616 16.1006 10.0304ZM11.3937 5.15334V4.54468C11.3937 4.42399 11.4976 4.32594 11.6255 4.32594H11.7736C11.9016 4.32594 12.0055 4.42399 12.0055 4.54468V5.15195C11.9894 5.15157 11.9719 5.1503 11.9564 5.1503L11.3937 5.15334ZM8.89732 7.64452H8.74922C8.73431 7.64452 8.72131 7.63905 8.70716 7.6365C8.60005 7.61722 8.51734 7.5324 8.51734 7.42577V4.54447C8.51734 4.42378 8.62128 4.32572 8.74922 4.32572H8.89732C9.02526 4.32572 9.1292 4.42378 9.1292 4.54447V7.42577C9.1292 7.5324 9.04649 7.61722 8.93938 7.6365C8.92523 7.63905 8.91224 7.64452 8.89732 7.64452ZM4.85661 6.48457H6.25265V7.18759C6.25265 7.22892 6.25967 7.26847 6.26558 7.30828H4.85661V6.48457ZM6.94829 5.82833V5.21417C6.94829 5.09347 7.05223 4.99542 7.18017 4.99542H7.32827C7.45621 4.99542 7.56015 5.09347 7.56015 5.21417V7.18759C7.56015 7.23321 7.54184 7.27321 7.51645 7.30828C7.47467 7.36601 7.40787 7.40634 7.32827 7.40634H7.18017C7.10058 7.40634 7.03378 7.36601 6.992 7.30828C6.96661 7.27321 6.94829 7.23321 6.94829 7.18759V5.82833ZM10.6669 7.29067C10.6633 7.29694 10.6586 7.30241 10.6543 7.30828C10.6125 7.36601 10.5457 7.40634 10.4661 7.40634H10.3183C10.2387 7.40634 10.1719 7.36601 10.1301 7.30828C10.1047 7.27321 10.0864 7.23321 10.0864 7.18759V6.94182C10.272 7.1121 10.4772 7.22035 10.6669 7.29067ZM10.0864 3.87499C10.0864 3.75429 10.1903 3.65624 10.3183 3.65624H10.4661C10.5941 3.65624 10.698 3.75429 10.698 3.87499V5.15712L10.0864 5.16043V3.87499ZM3.89942 10.0304C3.89942 10.1616 3.78619 10.2684 3.64715 10.2684H3.01695C2.87792 10.2684 2.76469 10.1616 2.76469 10.0304V4.00914C2.76469 3.87798 2.87792 3.77117 3.01695 3.77117H3.64715C3.78619 3.77117 3.89942 3.87798 3.89942 4.00914V10.0304ZM1.19564 8.84421V5.19537C1.19564 4.95419 1.40375 4.75788 1.65941 4.75788H2.06905V9.2817H1.65941C1.40375 9.2817 1.19564 9.08539 1.19564 8.84421Z" />
</g>
<defs>
<clipPath id="clip0_1475_17084">
<rect width="19" height="14"  transform="translate(0.5 3)"/>
</clipPath>
</defs>
</svg>
</span>Training Weekends</span><ul class="mm-listview"><li><!-- Processing title: Brecon Beacons Training Weekend -->
<a href="https://evertrek.co.uk/holidays/uk-training-weekend">Brecon Beacons Training Weekend</a></li><li><!-- Processing title: Scottish Winter Skills -->
<a href="https://evertrek.co.uk/holidays/scotland-winter-skills-weekend">Scottish Winter Skills</a></li><li><!-- Processing title: Snowdonia Training Weekend -->
<a href="https://evertrek.co.uk/holidays/north-wales-training-weekend">Snowdonia Training Weekend</a></li></ul></li><li class="has-submenu parent no-click"><!-- Processing title: Epic Peaks -->
<span><span class="zen-menu__icon"><svg fill="currentColor" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1475_17095)">
<path d="M16.8398 7.00778C16.8218 6.99636 14.9201 5.79033 13.4831 4.07037C11.2317 1.37279 9.77667 0.358626 6.09958 0.0319926C4.8408 -0.0799307 3.93845 0.10737 3.41959 0.587041C3.01128 0.966209 3 1.38649 3 1.43446C3 1.56694 3.07444 1.688 3.19401 1.7451L4.38737 2.31386C4.38737 2.31386 4.40316 2.32071 4.40993 2.32299C4.44151 2.33441 5.19046 2.64277 5.21302 3.58156L5.23783 4.67338C5.0709 4.78074 4.92878 4.92692 4.8205 5.10509C4.79117 5.13706 4.76861 5.17361 4.75056 5.21472L4.03319 7.07174C3.99936 7.15625 4.00161 7.25218 4.03771 7.33441C4.06026 7.38695 4.10087 7.43035 4.14599 7.46461C4.06929 7.69988 3.5166 9.50892 3.89333 11.6309C4.20464 13.3965 5.36416 17.7044 5.84692 19.4747C5.91009 19.7076 6.08379 19.8904 6.31163 19.9635C6.38157 19.9863 6.45375 19.9977 6.52594 19.9977C6.68611 19.9977 6.84402 19.9429 6.97035 19.8378L8.15018 18.8648C8.3893 18.6684 8.49984 18.344 8.42765 18.0379C8.42765 18.0311 8.42314 18.0219 8.42088 18.0151L6.55753 11.8867C6.55076 11.8662 5.94618 9.91779 6.53045 8.41025C6.56655 8.42395 6.6049 8.43081 6.64099 8.43081C6.77635 8.43081 6.90493 8.34858 6.95682 8.21381L7.61553 6.50755C7.6223 6.49156 7.63132 6.47786 7.63809 6.45959C7.64712 6.43446 7.65614 6.40705 7.66516 6.37964L7.67419 6.35451C7.68547 6.3271 7.68998 6.2997 7.69449 6.27228C7.73961 6.07128 7.74186 5.86571 7.69449 5.66699L8.37802 4.94063C8.40284 4.91322 9.01192 4.29421 9.77667 4.5843C9.99323 4.66653 10.3339 4.93606 10.7264 5.24899C11.8566 6.14666 13.729 7.63821 16.4857 7.63821C16.5466 7.63821 16.6052 7.63821 16.6661 7.63821C16.815 7.63364 16.9459 7.53314 16.9842 7.38467C17.0226 7.23848 16.9639 7.08316 16.8353 7.00321L16.8398 7.00778ZM11.1482 4.7145C10.6926 4.3536 10.3339 4.06808 10.018 3.94702C9.78795 3.86022 9.56913 3.82596 9.3661 3.82596C8.51563 3.82596 7.92459 4.4404 7.89301 4.47238L7.35385 5.04341C7.22301 4.90636 7.0651 4.79673 6.88463 4.72592L6.4312 4.54775C6.262 4.48151 6.08605 4.4541 5.91234 4.45867L5.89204 3.56557C5.86046 2.22934 4.81824 1.74967 4.6671 1.688L3.7467 1.24944C3.78053 1.19462 3.82791 1.13524 3.89784 1.07356C4.15952 0.845149 4.7438 0.598461 6.04093 0.712669C9.51047 1.02103 10.8121 1.93012 12.9665 4.50892C13.8079 5.51623 14.778 6.34081 15.4886 6.88215C13.5124 6.58978 12.1205 5.48425 11.1505 4.71221L11.1482 4.7145ZM7.72607 18.3349L6.54625 19.3079C6.54625 19.3079 6.53271 19.3171 6.51918 19.3125C6.50564 19.3079 6.50338 19.2988 6.50113 19.2942C6.40638 18.9447 6.25298 18.3851 6.07477 17.7159L7.37641 16.8981L7.77345 18.2047C7.78247 18.2526 7.76442 18.3029 7.72607 18.3349ZM5.02127 7.09229L4.79117 7.00321L4.93103 6.64232L5.08218 6.25401L5.21528 5.90682L5.26265 5.78348L5.30777 5.80175L5.77022 5.9822L6.88463 6.41619L6.92298 6.42989L6.73574 6.91185L6.5846 7.30244L6.44924 7.64963L6.20561 7.55369L5.01901 7.09001L5.02127 7.09229ZM5.67548 5.20787C5.70706 5.19417 5.73864 5.18046 5.77022 5.17133C5.83113 5.15305 5.89204 5.14392 5.9552 5.14392C6.0319 5.14392 6.11086 5.15762 6.18756 5.18731L6.64099 5.36548C6.7786 5.41801 6.88688 5.51623 6.96133 5.64414C6.97486 5.66699 6.9884 5.68983 6.99968 5.71267C7.00193 5.71724 7.00193 5.72409 7.00419 5.72866L5.67322 5.21016L5.67548 5.20787ZM4.77538 7.72957L5.90106 8.16813C5.23107 9.9018 5.88527 12.0009 5.9146 12.0946L7.16887 16.2198L5.88978 17.0215C5.38672 15.1188 4.76861 12.6907 4.55881 11.5076C4.23171 9.65739 4.68289 8.02651 4.77312 7.725L4.77538 7.72957Z" />
<path d="M7.32469 1.55778C7.02466 1.58291 6.75395 1.72681 6.55995 1.95979C6.36594 2.19278 6.27345 2.48743 6.30052 2.79122C6.32534 3.09501 6.46746 3.36911 6.69756 3.56555C6.90284 3.73914 7.1555 3.83279 7.41944 3.83279C7.45328 3.83279 7.48486 3.83279 7.5187 3.82823C8.13681 3.77341 8.59701 3.22064 8.54287 2.59478C8.51805 2.29099 8.37593 2.0169 8.14583 1.82046C7.91573 1.62402 7.62247 1.53037 7.32469 1.55778ZM7.13294 3.04019C7.04045 2.96253 6.98405 2.85289 6.97503 2.73183C6.96601 2.61077 7.0021 2.49428 7.0788 2.40063C7.1555 2.30698 7.26378 2.24988 7.38335 2.24074C7.39688 2.24074 7.41042 2.24074 7.42169 2.24074C7.65179 2.24074 7.84806 2.41891 7.86836 2.65646C7.87964 2.77752 7.84129 2.89401 7.76459 2.98766C7.68789 3.08131 7.57961 3.13841 7.46004 3.14755C7.34048 3.15897 7.22543 3.12014 7.13294 3.04248V3.04019Z" />
</g>
<defs>
<clipPath id="clip0_1475_17095">
<rect width="14" height="20"  transform="translate(3)"/>
</clipPath>
</defs>
</svg>
</span>Epic Peaks</span><ul class="mm-listview"><li><!-- Processing title: Kilimanjaro The Long Way -->
<a href="https://evertrek.co.uk/holidays/kilimanjaro-the-long-way">Kilimanjaro The Long Way</a></li><li><!-- Processing title: Ultimate Island Peak &amp;amp; Everest Base Camp -->
<a href="https://evertrek.co.uk/holidays/ultimate-island-peak-and-everest-base-camp-expedition">Ultimate Island Peak &amp; Everest Base Camp</a></li><li><!-- Processing title: Ultimate Mera Peak -->
<a href="https://evertrek.co.uk/holidays/ultimate-mera-peak-expedition">Ultimate Mera Peak</a></li><li><!-- Processing title: Island Peak Expedition -->
<a href="https://evertrek.co.uk/holidays/island-peak-expedition">Island Peak Expedition</a></li><li><!-- Processing title: Toubkal Roof of the North Weekender -->
<a href="https://evertrek.co.uk/holidays/mt-toubkal-roof-of-the-north-weekender">Toubkal Roof of the North Weekender</a></li><li><!-- Processing title: Mount Olympus Weekender -->
<a href="https://evertrek.co.uk/holidays/mt-olympus-myths-and-legends-weekender">Mount Olympus Weekender</a></li></ul></li><li class="has-submenu parent no-click"><!-- Processing title: Remote Adventures -->
<span><span class="zen-menu__icon"><svg fill="currentColor" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1475_17103)">
<path d="M11.6097 14.2995C12.9636 12.0134 14.8529 8.49982 14.8529 6.69444C14.8529 3.83021 12.6759 1.5 10 1.5C7.32408 1.5 5.14706 3.83021 5.14706 6.69444C5.14706 8.49982 7.03638 12.0134 8.39034 14.2995C5.77667 14.4538 2.5 15.0012 2.5 16.375C2.5 18.0559 7.40593 18.5 10 18.5C12.5941 18.5 17.5 18.0559 17.5 16.375C17.5 15.0012 14.2233 14.4538 11.6097 14.2995ZM3.16176 16.375C3.16176 15.9397 5.14119 15.1311 8.80044 14.9823C9.30829 15.8164 9.67787 16.3823 9.72922 16.4605C9.79126 16.5548 9.89229 16.6111 10 16.6111C10.1077 16.6111 10.2087 16.5548 10.2708 16.4605C10.3221 16.3823 10.6917 15.8164 11.1996 14.9823C14.8588 15.1311 16.8382 15.9397 16.8382 16.375C16.8382 16.8558 14.4245 17.7917 10 17.7917C5.57553 17.7917 3.16176 16.8558 3.16176 16.375ZM10 2.20833C12.311 2.20833 14.1912 4.22081 14.1912 6.69444C14.1912 8.40929 12.1644 12.0636 10.8427 14.2644C10.7706 14.3845 10.7004 14.5005 10.633 14.6114C10.5581 14.7344 10.4867 14.851 10.4187 14.9612C10.2549 15.2268 10.1119 15.4549 10 15.6314C9.88814 15.4549 9.74505 15.2268 9.58128 14.9612C9.51332 14.851 9.44191 14.7344 9.36705 14.6114C9.29957 14.5005 9.22945 14.3845 9.15734 14.2644C7.83564 12.0636 5.80882 8.40929 5.80882 6.69444C5.80882 4.22081 7.68899 2.20833 10 2.20833Z" />
<path d="M10.0003 9.99999C11.7639 9.99999 13.1988 8.46412 13.1988 6.57638C13.1988 4.68865 11.7639 3.15277 10.0003 3.15277C8.23666 3.15277 6.80176 4.68865 6.80176 6.57638C6.80176 8.46412 8.23666 9.99999 10.0003 9.99999ZM10.0003 3.8611C11.399 3.8611 12.5371 5.07924 12.5371 6.57638C12.5371 8.07352 11.399 9.29166 10.0003 9.29166C8.60158 9.29166 7.46352 8.07352 7.46352 6.57638C7.46352 5.07924 8.60158 3.8611 10.0003 3.8611Z" />
</g>
<defs>
<clipPath id="clip0_1475_17103">
<rect width="15" height="17"  transform="translate(2.5 1.5)"/>
</clipPath>
</defs>
</svg>
</span>Remote Adventures</span><ul class="mm-listview"><li><!-- Processing title: K2 Base Camp -->
<a href="https://evertrek.co.uk/holidays/k2-base-camp-trek">K2 Base Camp</a></li><li><!-- Processing title: Machu Picchu via Tomacaya -->
<a href="https://evertrek.co.uk/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley">Machu Picchu via Tomacaya</a></li><li><!-- Processing title: Annapurna Circuit -->
<a href="https://evertrek.co.uk/holidays/annapurna-circuit-trek">Annapurna Circuit</a></li><li><!-- Processing title: Everest Three Passes Trek -->
<a href="https://evertrek.co.uk/holidays/everest-three-passes-trek">Everest Three Passes Trek</a></li><li><!-- Processing title: Langtang Valley -->
<a href="https://evertrek.co.uk/holidays/langtang-valley-trek">Langtang Valley</a></li></ul></li><li class="has-submenu parent no-click"><!-- Processing title: Hardcore Challenges -->
<span><span class="zen-menu__icon"><svg fill="currentColor" width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1060_2283)">
<path d="M28.5713 22.1507V6.08651C28.5713 4.66016 27.4066 3.5 25.9747 3.5C24.5428 3.5 23.3782 4.66016 23.3782 6.08651V9.8743C23.0514 9.6997 22.6785 9.6001 22.2824 9.6001C21.7081 9.6001 21.1827 9.80955 20.7764 10.1544C20.3701 9.80955 19.8447 9.6001 19.2704 9.6001C18.6961 9.6001 18.1707 9.80955 17.7644 10.1544V6.08651C17.7644 4.66016 16.5997 3.5 15.1678 3.5C13.736 3.5 12.5713 4.66016 12.5713 6.08651V17.9279C12.5713 18.0512 12.583 18.1715 12.5998 18.2902C12.5826 18.4515 12.5713 18.6039 12.5713 18.7289V22.1507C12.5713 24.398 14.0593 26.3966 16.1735 27.0795V28.9767H15.619C14.7327 28.9767 14.012 29.6177 14.012 30.4055V35.887C14.012 36.2254 14.2876 36.5 14.6274 36.5H27.5957C27.9355 36.5 28.2111 36.2254 28.2111 35.887V30.4055C28.2111 29.6177 27.4904 28.9767 26.6045 28.9767H26.0497V26.5874C27.6142 25.6519 28.5713 23.9849 28.5713 22.1507ZM26.9803 30.4055V35.274H15.2428V30.4055C15.2428 30.3341 15.3858 30.2027 15.619 30.2027H26.6045C26.8377 30.2027 26.9803 30.3341 26.9803 30.4055ZM24.8189 26.2262V28.9767H17.4042V26.6113C17.4042 26.3232 17.2031 26.0742 16.9206 26.0127C15.1138 25.6184 13.8021 23.9941 13.8021 22.1507V18.7289C13.8021 18.6125 13.8189 18.4373 13.8441 18.2474C13.872 18.037 13.9104 17.8102 13.9492 17.6211L13.9365 17.6127C13.9441 17.6014 13.951 17.5897 13.9579 17.5778C13.9941 17.5148 14.0211 17.4452 14.0328 17.3692C14.0463 17.282 14.0684 17.2048 14.0869 17.1233C14.3228 16.0862 14.9441 15.505 15.5704 15.1828C15.9145 15.0057 16.2552 14.904 16.5336 14.8477C16.6983 14.8145 16.8384 14.7956 16.9439 14.7858C17.0543 14.7756 17.1299 14.7732 17.1454 14.7731L17.1491 14.7731L17.5594 14.7755L17.7644 14.7767L18.1747 14.7791L18.4895 14.7809L19.9559 14.7894L20.3662 14.7918L20.4543 14.7923C20.4496 14.8242 20.4417 14.8516 20.4363 14.8825C20.417 14.9927 20.3939 15.0972 20.3662 15.1949C20.2692 15.5366 20.1246 15.804 19.9559 16.0138C19.3754 16.7358 18.5168 16.7757 18.3746 16.7765H17.149C17.0767 16.7765 17.0083 16.7913 16.9439 16.8141C16.7055 16.8986 16.5336 17.1232 16.5336 17.3895V18.489C16.5336 18.6523 16.5989 18.8091 16.7155 18.9241C16.7483 18.9565 16.7851 18.9837 16.8237 19.0077C16.9148 19.0644 17.0194 19.0962 17.1279 19.1C17.135 19.1002 17.1419 19.102 17.149 19.102H17.1534C17.264 19.1024 17.3637 19.1162 17.4674 19.1251C17.613 19.1375 17.7514 19.1584 17.8829 19.1878C18.3017 19.2814 18.6552 19.4524 18.9178 19.7246C18.9658 19.7743 19.0099 19.8262 19.0513 19.8795C19.2041 20.076 19.3144 20.2935 19.3938 20.5082C19.5057 20.8108 19.5562 21.1058 19.5789 21.3293C19.602 21.5568 19.5967 21.7102 19.5965 21.7145C19.5793 22.0517 19.8389 22.3395 20.1775 22.3578C20.1891 22.3582 20.2003 22.3586 20.2115 22.3586C20.536 22.3586 20.8077 22.1056 20.8257 21.7791C20.8281 21.7317 20.8621 20.967 20.5605 20.124C20.6359 20.0738 20.7075 20.0186 20.7764 19.9601C21.1827 20.305 21.7081 20.5144 22.2824 20.5144C23.5653 20.5144 24.6089 19.4748 24.6089 18.1969V6.08651C24.6089 5.33622 25.2215 4.72601 25.9747 4.72601C26.7279 4.72601 27.3405 5.33622 27.3405 6.08651V22.1507C27.3405 23.6485 26.5036 25.0006 25.1566 25.6791C24.9495 25.7836 24.8189 25.9951 24.8189 26.2262ZM13.8021 6.08651C13.8021 5.33622 14.4146 4.72601 15.1678 4.72601C15.921 4.72601 16.5336 5.33622 16.5336 6.08651V13.5976C15.6766 13.7262 14.5914 14.1297 13.8021 15.0169V6.08651ZM18.1747 11.9176C18.1747 11.3158 18.6662 10.8261 19.2704 10.8261C19.5906 10.8261 19.8764 10.9661 20.077 11.1849C19.9995 11.4155 19.9559 11.6614 19.9559 11.9176V13.5632L18.1747 13.553V11.9176ZM23.3782 11.9176V18.1969C23.3782 18.7987 22.8866 19.2884 22.2824 19.2884C21.9622 19.2884 21.6764 19.1485 21.4759 18.9296C21.298 18.7355 21.1867 18.4798 21.1867 18.1969V16.3997C21.3701 16.0911 21.5073 15.7453 21.5969 15.3631C21.6022 15.3408 21.6061 15.3173 21.611 15.2947C21.6555 15.0905 21.6896 14.8783 21.7077 14.6541C21.72 14.5014 21.7271 14.3447 21.7271 14.1829C21.7271 14.1001 21.7103 14.0213 21.6805 13.9492C21.6594 13.8985 21.6302 13.8526 21.5969 13.8098C21.499 13.6836 21.3538 13.5979 21.1867 13.5774V11.9176C21.1867 11.6347 21.298 11.3791 21.4759 11.1849C21.6764 10.9661 21.9622 10.8261 22.2824 10.8261C22.8866 10.8261 23.3782 11.3158 23.3782 11.9176ZM19.9529 19.044C19.908 18.9901 19.8633 18.936 19.8137 18.8841C19.3998 18.4515 18.8834 18.1566 18.2712 18.0026H18.3694C18.4041 17.9962 19.1646 17.9994 19.9559 17.5777V18.1969C19.9559 18.4531 19.9995 18.699 20.077 18.9296C20.0388 18.9712 19.997 19.0086 19.9529 19.044Z" />
</g>
<defs>
<clipPath id="clip0_1060_2283">
<rect width="16" height="33" fill="white" transform="translate(12.5713 3.5)"/>
</clipPath>
</defs>
</svg>
</span>Hardcore Challenges</span><ul class="mm-listview"><li><!-- Processing title: K2 Base Camp Trek -->
<a href="https://evertrek.co.uk/holidays/k2-base-camp-trek">K2 Base Camp Trek</a></li><li><!-- Processing title: Ultimate Island Peak and Everest Base Camp -->
<a href="https://evertrek.co.uk/holidays/ultimate-island-peak-and-everest-base-camp-expedition">Ultimate Island Peak and Everest Base Camp</a></li><li><!-- Processing title: Ultimate Mera Peak -->
<a href="https://evertrek.co.uk/holidays/ultimate-mera-peak-expedition">Ultimate Mera Peak</a></li></ul></li></ul></li><li><!-- Processing title: Knowledge Centre -->
<a href="/knowledge-centre" title="" class="" rel="">Knowledge Centre</a></li><li class="has-submenu parent"><!-- Processing title: About Us -->
<a href="/about-us" title="" class="" rel="">About Us</a><ul class="mm-listview"><li><a href="/about-us">About Us</a></li><li><a href="/about-us/download-our-adventure-brochure">Download our Adventure Brochure</a></li><li><a href="/about-us/reasons-to-choose-evertrek">Reasons to choose EverTrek</a></li><li><a href="/evertrekker-rewards">Refer and Earn - EverTrekker Rewards </a></li><li><a href="/about-us/evertrek-partnerships">EverTrek Partnerships</a></li><li><a href="/about-us/charity-projects">Charity Projects</a></li><li><a href="/about-us/responsible-travel">Responsible Travel</a></li><li><a href="/about-us/evertrek-reviews">EverTrek Reviews</a></li><li><a href="/about-us/ask-us-anything-with-yet-ai-bot">YETI-AI Ask us Anything</a></li></ul></li><li><!-- Processing title: Contact -->
<a href="/contact" title="" class="" rel="">Contact</a></li><li><!-- Processing title: Search -->
<a href="/holidays#/holidays?sort=ordering%7CASC" title="" class="" rel="">Search</a></li></ul>
<style>
/* Add cursor styling for non-clickable menu items */
.mm-listview .mm-listitem.has-submenu > a,
.mm-listview .mm-listitem.parent > a,
.mm-listview .mm-listitem > a:not([href]),
.mm-listview .mm-listitem > span {
    cursor: default;
    color: white;
    opacity: 0.8;
}

/* Ensure links with href have pointer cursor */
.mm-listview .mm-listitem > a[href] {
    cursor: pointer;
}

/* Style for non-clickable menu items */
.mm-listview .mm-listitem.no-click > span {
    cursor: default;
    display: block;
    padding: 12px;
    color: white;
    opacity: 0.8;
}
</style>





<div class="custom"  >
	<p><a class="zen-pill zen-pill--dark d-flex align-items-center justify-content-center mt-3" href="https://evertrek-booking.vercel.app/">Members Login</a></p></div>

          </div>

    <!-- Page wrapper -->
    <div id="page" class="zen-page">
      <div class="zen-overlay"></div>
      <!------------------------------------------------------------------------------
      // Site Notice
      //----------------------------------------------------------------------------->
      
      <div class="zen-header-container">
        <div class="zen-utility-bar">
          <div class="top-whatsapp d-flex d-md-none">
            <a href="https://api.whatsapp.com/send/?phone=%2B447897030466" class="fg-white text-decoration-none"><img src="/templates/zenbase/icons/whatsapp.svg" alt=""> WhatsApp us</a>
          </div>
          <div class="top-social d-none d-md-flex">
            <a href="https://www.facebook.com/evertrekuk/" title="Facebook" target="_blank"><img src="/templates/zenbase/icons/social-facebook.svg" alt="Facebook"></a>
            <a href="https://www.instagram.com/evertrekuk/" title="Instagram" target="_blank"><img src="/templates/zenbase/icons/social-instagram.svg" alt="Instagram"></a>
            <a href="https://www.youtube.com/@EverTrekUK" title="Youtube" target="_blank"><img src="/templates/zenbase/icons/social-youtube.svg" alt="Youtube"></a>
            <a href="https://twitter.com/evertrekuk" title="Twitter" target="_blank"><img src="/templates/zenbase/icons/social-twitter.svg" alt="Twitter"></a>
          </div>
          <div class="top-social d-flex d-md-none">
            <a href="https://www.facebook.com/evertrekuk/" title="Facebook"><img src="/templates/zenbase/icons/social-facebook.svg" alt="Facebook"></a>
            <a href="https://www.instagram.com/evertrekuk/" title="Instagram"><img src="/templates/zenbase/icons/social-instagram.svg" alt="Instagram"></a>
            <a href="https://www.youtube.com/@EverTrekUK" title="Youtube"><img src="/templates/zenbase/icons/social-youtube.svg" alt="Youtube"></a>
            <a href="https://twitter.com/evertrekuk" title="Twitter"><img src="/templates/zenbase/icons/social-twitter.svg" alt="Twitter"></a>
          </div>

          <div class="top-ratings d-none d-md-flex">
            <a class="fg-white text-decoration-none" href="https://uk.trustpilot.com/review/bucketlistadventuretravel.co.uk" target="_blank">
              <span>Excellent</span>
              <span><strong>4.7</strong> out of 5</span>
              <img src="/templates/zenbase/icons/trustpilot.svg" alt="Trustpilot">
            </a>
          </div>
          <div class="top-login">
            <a href="https://evertrek-booking.vercel.app/" class="fg-white zen-link zen-link--no-underline">Login <img src="/templates/zenbase/icons/login.svg" alt="Login"></a>
          </div>
        </div>

        <div class="top-offer-bar offer-bar">
<div class="flex-container">
<a class="cover"href="/destinations/asia/nepal"></a>
<div class="central-container">
<p class="old-growth">Epic Everest Offer</p>
<p class="small">
<span class="line-1">£100 off <strong>Nepal Trips +</strong></span> <span class="line-2">£75 <strong>Ellis Brigham Kit Voucher</strong> 
<a id="openCampaignModal"></a>
<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width="100%" height="100%">
  <defs>
    <filter id="drop-shadow">
      <feDropShadow dx="0" dy="0" stdDeviation="2" flood-color="rgba(0, 0, 0, 0.75)"/>
    </filter>
  </defs>
  <path fill="none" d="M0 0h24v24H0V0Z"/>
  <path filter="url(#drop-shadow)" d="M11 7h2v2h-2Zm0 4h2v6h-2Zm1-9C6.48 2 2 6.48 2 12c0 5.52 4.48 10 10 10s10-4.48 10-10 -4.48-10-10-10Zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8 -3.59 8-8 8Z" fill="white"/>
</svg>
</span></p>
<p class="smaller">Use code EVEREST175</p>
<!--<p class="sub"><span class="campaign-end-date">UNTIL 31st March</span></p>-->
</div></div></div>

<style>

.top-offer-bar {
box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.48), 0px 4px 10px 0px rgba(0, 0, 0, 0.16);
z-index: 2;
}

.top-offer-bar .fg-yellow {
    color: #f5c243;
}
.top-offer-bar .line-2 {
position:relative;
}
.top-offer-bar .line-2 a { 
position:absolute;
top:-10px;
right:-10px;
bottom: -10px;
width: 38px;
z-index: 4;
}
.top-offer-bar .line-2 svg { 
    align-items: center;
    display: inline-flex;
    height: 19px;
    width: 19px;
    margin-top: -5px;
    position: relative; 
    top: 2px;
    z-index: 3;
}

.old-growth { font-family: "Old Growth"!important; text-transform: none!important; }

.top-offer-bar p {
    color: white;
    text-align: center;
    font-size: 24px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-transform: none;
    margin-bottom: 0;
    text-shadow: 0px 0px 7px rgba(0,0,0,.75);
    margin: 0 auto 10px auto;
}

.top-offer-bar p:first-of-type {
  margin-bottom: 5px;
}

.top-offer-bar p strong {
    color: #FFBF06;
}

.top-offer-bar p.sub { font-size: 24px; margin-bottom: 0; }
.top-offer-bar p.small {
    color: var(--Primary-White, #FFF);
    text-align: center;
    font-size: 18px;
    font-style: normal;
    font-weight: 700;
    line-height: normal;
    text-transform: none;
    text-shadow: 0px 0px 7px rgba(0,0,0,.75);
    margin: -5px 0 5px 0;
}
.top-offer-bar p.small strong {
    font-size: 18px;
}
.top-offer-bar p.smaller {
    color: var(--Primary-White, #FFF);
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: normal;
    text-transform: none;
    text-shadow: 0px 0px 7px rgba(0,0,0,.75);
    margin: -5px 0 5px 0;
}

.top-offer-bar small, .campaign-end-date {
    line-height: normal;
    color: white;
    font-family: 'IBM Plex Sans SemiBold', Helvetica, Arial, sans-serif;
    text-shadow: 0px 0px 7px rgba(0,0,0,.75);

}

.hide-top-bar .top-offer-bar {
    display: none;
}

.top-offer-bar a:hover {
    color: #ccc;
}

.light-link {
    text-decoration: none;
    border-bottom: 1px solid rgba(0, 0, 0, .25);
    padding-bottom: 0;
    margin-bottom: 0;
}

.fg-black {
    color: black;
}

.fg-white {
    color: white;
}

.fg-grey {
    color: #969696;
}

.fg-pink {
    color: #D72555;
}

.bg-yellow {
    background-color: #ffc105;
}

.bg-black {
    background-color: black;
}

.zen-holiday__tabs {
    top: 172px !important;
}


@media screen and (max-width: 840px) {
/* was 767px */

    html {
        margin-top: 0!important;
    }

    .central-container {
        width: 90%;
        padding: 5px 0 0 0 !important;
        margin: 0 -14px 0 -9px;
    }

    .top-offer-bar p {
        font-size: 16px;
        line-height: normal;
        padding: 0 20px !important;
        margin-bottom: 0;
    }

    .top-offer-bar p.sub {
        font-size: 24px;
        line-height: normal;
		/* margin: 5px 0; */
    }
    .top-offer-bar p.small {
        font-size: 16px;
        line-height: normal;
        padding: 8px 20px 0 20px!important;
    }
    .top-offer-bar p.smaller{
        font-size: 14px;
        line-height: normal;
        padding: 8px 20px 0 20px!important;
    }

    .top-offer-bar a {
        font-size: 24px;
        line-height: 25px;
        margin: 0 auto 0 0;
    }

    .top-offer-bar small, .campaign-end-date {
        /* font-size: 10px; */
        line-height: normal;
    }

    .med-up {
        display: none
    }

    .top-offer-bar {
        background-image: url(/images/2025/05/02/maybannerbgmobile1400.webp) !important;
        height: 122px!important;
        background-position: 50% 50%!important;
    }
    .homepage .zen-header {
        top: 134px !important;
    }


.line-1, .line-2 {font-size: 16px; display:block; margin: 0 auto; }
/* 
.line-2 {font-size: 12px; display:block; margin: 0 auto; }
.line-3 {font-size: 10px; display:block; margin: 0 auto; }
*/

}

@media screen and (max-width: 640px) {
    html {
        margin-top: 112px;
    }
div#facets {
    max-height: 440px;
}

}


.top-offer-bar {
    min-height: 96px;
    background-image: url(/images/2025/05/02/maybannerbgdesktop.webp);
    background-size: cover;
    background-position: 50% 50%;
    display: flex;
    flex-direction: column;
    position: relative;
    align-items: center;
    height: 96px;
}

.nowrap {
    white-space: nowrap
}


.flex-container {
    position: relative;
    display: flex;
    align-items: stretch;
    justify-content: center;
    width: 100%;
    height: 100%;
    flex: 1;
}

.central-container {
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0 40px;
    /* 20px gap on either side */
    /* white-space: nowrap;  Fit to content width */
}

a.cover {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}
@media screen and (min-width: 992px) {
.top-offer-bar {
background-position: 50% 20%; 
}
.homepage .zen-header {
    top: 158px!important;
} 

}

/* CLOCKS EDITS */
/* .top-offer-bar p:first-of-type { font-size: 20px; line-height: normal; margin: 7px 0 9px 0; }
.top-offer-bar p.small { font-size: 16px; line-height: normal; }
.top-offer-bar p.sub { margin: -5px 0 5px 0; }

@media screen and (max-width: 840px) {
.top-offer-bar p:first-of-type { font-size: 12px; line-height: normal; margin: 3px 0 7px 0; }
.top-offer-bar p.small { font-size: 15px; line-height: normal; padding: 0 20px !important;}
.top-offer-bar p.sub { font-size: 20px; line-height: normal; margin: -2px 0 5px 0; }
.line-2, .line-3 { font-size: 11px; line-height: normal; }
.top-offer-bar {height: 131px!important;}
.central-container { margin: 0; }
}
*/
</style>
        <!------------------------------------------------------------------------------
        // Header
        //----------------------------------------------------------------------------->
        <header class="zen-header">
          <div class="zen-header__main">
            <div class="container">
            <div class="mx-auto">
              <div class="nav-wrapper mx-auto">
                <div class="zen-header__logo">
                                      

<div class="custom"  >
	<p><a title="Home Page" href="https://evertrek.co.uk/"><img class="img-fluid" src="/images/evertrek-logo-horizontal.svg" alt="Evertrek Logo" width="237" /></a></p></div>

                                  </div>
                <div class="zen-header__menu">
                  <!------------------------------------------------------------------------------
                  // Menu
                  //----------------------------------------------------------------------------->
                  <div class="zen-menu justify-content-end align-items-center">
                    <div class="zen-menu__main">
                                              <div class="d-none d-xl-block justify-content-end align-items-center h-100">
                          <!-- Setting empty flink for Destinations (ID: 112) -->
<!-- Setting empty flink for Collections (ID: 605) -->
<style>
/* Custom dropdown menu styles */
.zen-menu {
    position: relative;
}

.zen-menu__list {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    margin-left: 75px;
    justify-content: space-between;
}

.zen-menu__item {
    position: relative;
}

.zen-menu__link {
    display: block;
    padding: 15px 20px;
    color: #333;
    text-decoration: none;
    white-space: nowrap;
    font-size: 16px;
    font-weight: 700;
}

.zen-menu__panel {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 0 0 10px 10px;
    min-width: 500px;
    height: auto;
    min-height: 50px;
    overflow: visible;
    padding: 20px;
    width: max-content;
    display: none;
}

.zen-menu__columns {
    display: flex;
    gap: 0;
    width: fit-content;
}

.zen-menu__column {
    display: flex;
    flex-direction: column;
    min-width: 190px;
    width: fit-content;
}

.zen-menu__column:first-child {
    padding-right: 15px;
    min-width: 190px;
}

.zen-menu__column:nth-child(2) {
    padding-left: 15px;
    padding-right: 15px;
    border-left: 1px solid rgba(0, 0, 0, 0.20);
    width: fit-content;
    /* flex: 0 0 220px; */
}

.zen-menu__column:nth-child(3) {
    padding-left: 15px;
    width: 420px;
    position: relative;
    border-left: 1px solid rgba(0, 0, 0, 0.20);
}

.zen-menu__sublist {
    list-style: none;
    margin: 0;
    padding: 10px 0;
    width: 100%;
}

.zen-menu__sublink {
    display: block;
    padding: 8px 20px;
    padding-right: 32px;
    color: #333;
    text-decoration: none;
    white-space: nowrap;
    position: relative;
    border-radius: 9999px;
    width: 100%;
    box-sizing: border-box;
    font-size: 14px;
    font-weight: 400;
}

.zen-menu__sublink:hover,
.zen-menu__sublink:focus,
.zen-menu__sublink.active {
    background-color: #e3e8ee;
    color: black;
    width: 100%;
}

.zen-menu__sublink:hover::after,
.zen-menu__sublink:focus::after,
.zen-menu__sublink.active::after {
    content: '';
    position: absolute;
    right: 7px;
    top: 50%;
    transform: translateY(-50%);
    width: 24px;
    height: 24px;
    background-image: url('/templates/zenbase/icons/keyboard_arrow_right.svg');
    background-repeat: no-repeat;
    background-position: center;
    background-size: contain;
}

/* Remove right arrow from Destinations menu third column, Collections menu second column, About Us menu items, and first-level Destinations and Collections menu items */
.zen-menu__item[data-item-id="112"] .zen-menu__column:nth-child(3) .zen-menu__sublink:hover::after,
.zen-menu__item[data-item-id="112"] .zen-menu__column:nth-child(3) .zen-menu__sublink:focus::after,
.zen-menu__item[data-item-id="112"] .zen-menu__column:nth-child(3) .zen-menu__sublink.active::after,
.zen-menu__item[data-item-id="605"] .zen-menu__column:nth-child(2) .zen-menu__sublink:hover::after,
.zen-menu__item[data-item-id="605"] .zen-menu__column:nth-child(2) .zen-menu__sublink:focus::after,
.zen-menu__item[data-item-id="605"] .zen-menu__column:nth-child(2) .zen-menu__sublink.active::after,
.zen-menu__item[data-item-id="362"] .zen-menu__sublink:hover::after,
.zen-menu__item[data-item-id="362"] .zen-menu__sublink:focus::after,
.zen-menu__item[data-item-id="362"] .zen-menu__sublink.active::after,
.zen-menu__panel[data-parent-id="362"] .zen-menu__sublink:hover::after,
.zen-menu__panel[data-parent-id="362"] .zen-menu__sublink:focus::after,
.zen-menu__panel[data-parent-id="362"] .zen-menu__sublink.active::after,
.zen-menu__item[data-item-id="112"] > .zen-menu__link:hover::after,
.zen-menu__item[data-item-id="112"] > .zen-menu__link:focus::after,
.zen-menu__item[data-item-id="112"] > .zen-menu__link.active::after,
.zen-menu__item[data-item-id="605"] > .zen-menu__link:hover::after,
.zen-menu__item[data-item-id="605"] > .zen-menu__link:focus::after,
.zen-menu__item[data-item-id="605"] > .zen-menu__link.active::after {
    content: none !important;
    background-image: none !important;
}

/* Highlight locking for menu items */
.zen-menu__column:nth-child(2) .zen-menu__sublink.active {
    background-color: #e3e8ee;
    color: black;
    border-radius: 9999px;
    position: relative;
    padding-right: 32px;
}

/* Keep highlight when moving between columns */
.zen-menu__column:nth-child(2) .zen-menu__sublink.active:hover,
.zen-menu__column:nth-child(2) .zen-menu__sublink.active:hover ~ .zen-menu__column:nth-child(3) {
    background-color: #e3e8ee;
    color: black;
}

/* Add cursor styling for non-clickable menu items */
.zen-menu__item[data-item-id="112"] .zen-menu__column:first-child .zen-menu__sublink,
.zen-menu__item[data-item-id="112"] .zen-menu__third-level .zen-menu__sublink,
.zen-menu__item[data-item-id="112"] > .zen-menu__link,
.zen-menu__item[data-item-id="605"] > .zen-menu__link,
.zen-menu__sublink--collection,
.zen-menu__item[data-item-id="605"] .zen-menu__column:first-child .zen-menu__sublink {
    cursor: default;
}

/* Ensure Collections menu third level items have pointer cursor */
.zen-menu__item[data-item-id="605"] .zen-menu__third-level .zen-menu__sublink {
    cursor: pointer;
}

/* Ensure About Us menu items have pointer cursor */
.zen-menu__item[data-item-id="362"] .zen-menu__sublink,
.zen-menu__item[data-parent-id="362"] .zen-menu__sublink,
.zen-menu__panel[data-parent-id="362"] .zen-menu__sublink {
    cursor: pointer !important;
}

/* About Us menu specific overrides */
/* Override min-width for About Us menu panel */
.zen-menu__panel[data-parent-id="362"] {
    min-width: auto;
}

/* Override padding-right for first column in About Us menu */
.zen-menu__item[data-item-id="362"] .zen-menu__column:first-child {
    padding-right: 0;
}

/* Collections menu specific overrides */
/* Make 3rd column <ul>s 100% width in Collections */
.zen-menu__item[data-item-id="605"] .zen-menu__column:nth-child(3) .zen-menu__sublist {
    width: 100% !important;
}

/* Override padding-right for 2nd column in Collections dropdown */
.zen-menu__item[data-item-id="605"] .zen-menu__column:nth-child(2) {
    padding-right: 0;
}

.zen-menu__arrow {
    margin-left: 5px;
}

/* Third level container */
.zen-menu__third-level {
    position: relative;
    width: 100%;
}

/* Ensure subitem has 100% width */
.zen-menu__subitem {
    width: 100%;
}

/* Fourth level container */
.zen-menu__fourth-level {
    position: relative;
}

/* Hide all third and fourth level menus by default */
.zen-menu__third-level > .zen-menu__sublist,
.zen-menu__fourth-level > .zen-menu__sublist {
    display: none;
}

/* Desktop hover behavior */
@media (min-width: 992px) {
    /* Show dropdown panel on hover */
    .zen-menu__item:hover .zen-menu__panel {
        display: flex;
    }

    /* Show first third level menu by default when panel is visible */
    .zen-menu__item:hover .zen-menu__third-level > .zen-menu__sublist:first-child {
        display: block;
    }

    /* Dynamic selectors for specific menu items */
    .zen-menu__column:first-child .zen-menu__subitem[data-item-id="636"]:hover ~ .zen-menu__column:nth-child(2) .zen-menu__third-level > .zen-menu__sublist[data-parent-id="636"],
    .zen-menu__column:first-child .zen-menu__subitem[data-item-id="637"]:hover ~ .zen-menu__column:nth-child(2) .zen-menu__third-level > .zen-menu__sublist[data-parent-id="637"],
    .zen-menu__column:first-child .zen-menu__subitem[data-item-id="638"]:hover ~ .zen-menu__column:nth-child(2) .zen-menu__third-level > .zen-menu__sublist[data-parent-id="638"],
    .zen-menu__column:first-child .zen-menu__subitem[data-item-id="639"]:hover ~ .zen-menu__column:nth-child(2) .zen-menu__third-level > .zen-menu__sublist[data-parent-id="639"],
    .zen-menu__column:first-child .zen-menu__subitem[data-item-id="113"]:hover ~ .zen-menu__column:nth-child(2) .zen-menu__third-level > .zen-menu__sublist[data-parent-id="113"],
    .zen-menu__column:first-child .zen-menu__subitem[data-item-id="115"]:hover ~ .zen-menu__column:nth-child(2) .zen-menu__third-level > .zen-menu__sublist[data-parent-id="115"],
    .zen-menu__column:first-child .zen-menu__subitem[data-item-id="114"]:hover ~ .zen-menu__column:nth-child(2) .zen-menu__third-level > .zen-menu__sublist[data-parent-id="114"],
    .zen-menu__column:first-child .zen-menu__subitem[data-item-id="468"]:hover ~ .zen-menu__column:nth-child(2) .zen-menu__third-level > .zen-menu__sublist[data-parent-id="468"] {
        display: block;
    }

    /* Show fourth level menus when hovering over third level items */
    .zen-menu__third-level .zen-menu__subitem[data-item-id]:hover ~ .zen-menu__column:nth-child(3) .zen-menu__fourth-level > .zen-menu__sublist[data-parent-id],
    .zen-menu__column:nth-child(3) .zen-menu__fourth-level:hover > .zen-menu__sublist {
        display: block;
    }
}
</style>

<!-- Main Menu Container -->
<div class="zen-menu">
    <!-- 1st LEVEL -->
    <ul class="zen-menu__list">
                    <li class="zen-menu__item" data-item-id="112">
            <!-- Processing title: Destinations -->
<!-- Forcing empty href for Destinations (ID: 112) -->
<!-- Using span for non-clickable item: Destinations (ID: 112) -->
<span class="zen-menu__link zen-menu__link--has-children" >Destinations<span class="zen-menu__arrow"><svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1060_3236)">
<path d="M16.59 9.09009L12 13.6701L7.41 9.09009L6 10.5001L12 16.5001L18 10.5001L16.59 9.09009Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_1060_3236">
<rect width="24" height="24" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
</span></span>
                            <!-- Dropdown Panel -->
                <div class="zen-menu__panel" data-parent-id="112">
                    <div class="zen-menu__columns">
                        <div class="zen-menu__column">
                            <!-- 2nd level -->
                            <ul class="zen-menu__sublist">
                                                                    <li class="zen-menu__subitem" data-item-id="113">
                                        <!-- Processing title: Asia -->
<!-- Using span for non-clickable item: Asia (ID: 113) -->
<span class="zen-menu__sublink" >Asia</span>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="115">
                                        <!-- Processing title: South America -->
<!-- Using span for non-clickable item: South America (ID: 115) -->
<span class="zen-menu__sublink" >South America</span>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="114">
                                        <!-- Processing title: Africa -->
<!-- Using span for non-clickable item: Africa (ID: 114) -->
<span class="zen-menu__sublink" >Africa</span>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="468">
                                        <!-- Processing title: Europe -->
<!-- Using span for non-clickable item: Europe (ID: 468) -->
<span class="zen-menu__sublink" >Europe</span>                                    </li>
                                                            </ul>
                        </div>

                                                    <div class="zen-menu__column">
                                <!-- 3rd level -->
                                <div class="zen-menu__third-level">
                                                                                                                        <ul class="zen-menu__sublist" data-parent-id="113">
                                                                                                    <li class="zen-menu__subitem" data-item-id="116">
                                                        <!-- Processing title: Nepal -->
<!-- Using span for non-clickable item: Nepal (ID: 116) -->
<span class="zen-menu__sublink" >Nepal</span>                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="454">
                                                        <!-- Processing title: Pakistan -->
<!-- Using span for non-clickable item: Pakistan (ID: 454) -->
<span class="zen-menu__sublink" >Pakistan</span>                                                    </li>
                                                                                            </ul>
                                                                                                                                                                <ul class="zen-menu__sublist" data-parent-id="115">
                                                                                                    <li class="zen-menu__subitem" data-item-id="141">
                                                        <!-- Processing title: Peru -->
<!-- Using span for non-clickable item: Peru (ID: 141) -->
<span class="zen-menu__sublink" >Peru</span>                                                    </li>
                                                                                            </ul>
                                                                                                                                                                <ul class="zen-menu__sublist" data-parent-id="114">
                                                                                                    <li class="zen-menu__subitem" data-item-id="117">
                                                        <!-- Processing title: Tanzania -->
<!-- Using span for non-clickable item: Tanzania (ID: 117) -->
<span class="zen-menu__sublink" >Tanzania</span>                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="118">
                                                        <!-- Processing title: Morocco -->
<!-- Using span for non-clickable item: Morocco (ID: 118) -->
<span class="zen-menu__sublink" >Morocco</span>                                                    </li>
                                                                                            </ul>
                                                                                                                                                                <ul class="zen-menu__sublist" data-parent-id="468">
                                                                                                    <li class="zen-menu__subitem" data-item-id="659">
                                                        <!-- Processing title: France -->
<!-- Using span for non-clickable item: France (ID: 659) -->
<span class="zen-menu__sublink" >France</span>                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="764">
                                                        <!-- Processing title: Greece -->
<!-- Using span for non-clickable item: Greece (ID: 764) -->
<span class="zen-menu__sublink" >Greece</span>                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="470">
                                                        <!-- Processing title: Italy -->
<!-- Using span for non-clickable item: Italy (ID: 470) -->
<span class="zen-menu__sublink" >Italy</span>                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="504">
                                                        <!-- Processing title: United Kingdom -->
<!-- Using span for non-clickable item: United Kingdom (ID: 504) -->
<span class="zen-menu__sublink" >United Kingdom</span>                                                    </li>
                                                                                            </ul>
                                                                                                            </div>
                            </div>
                        
                                                <div class="zen-menu__column">
                            <div class="zen-menu__fourth-level">
                                                                            <ul class="zen-menu__sublist" data-parent-id="116">
                                                                                                    <li class="zen-menu__subitem">
                                                        <a href="/holidays/everest-base-camp-trek" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Everest Base Camp Trek -->
Everest Base Camp Trek                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/everest-three-passes-trek" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Everest Three Passes Trek -->
Everest Three Passes Trek                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/everest-base-camp-via-gokyo-valley" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Everest Base Camp Via Gokyo Valley -->
Everest Base Camp Via Gokyo Valley                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/ultimate-island-peak-and-everest-base-camp-expedition" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Ultimate Island Peak and Everest Base Camp Expedition -->
Ultimate Island Peak and Everest Base Camp Expedition                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/annapurna-base-camp-trek" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Annapurna Base Camp Trek -->
Annapurna Base Camp Trek                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/langtang-valley-trek" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Langtang Valley Trek -->
Langtang Valley Trek                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/ultimate-mera-peak-expedition" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Ultimate Mera Peak Expedition -->
Ultimate Mera Peak Expedition                                                        </a>
                                                    </li>
                                                                                                                                                                                                                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/island-peak-expedition" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Island Peak Expedition -->
Island Peak Expedition                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/annapurna-circuit-trek" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Annapurna Circuit Trek -->
Annapurna Circuit Trek                                                        </a>
                                                    </li>
                                                                                                                                                </ul>
                                                                                        <ul class="zen-menu__sublist" data-parent-id="454">
                                                                                                    <li class="zen-menu__subitem">
                                                        <a href="/holidays/k2-base-camp-trek" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: K2 Base Camp Trek -->
K2 Base Camp Trek                                                        </a>
                                                    </li>
                                                                                                                                                </ul>
                                                                                        <ul class="zen-menu__sublist" data-parent-id="141">
                                                                                                    <li class="zen-menu__subitem">
                                                        <a href="/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Machu Picchu via Tomacaya route - The Hidden Valley -->
Machu Picchu via Tomacaya route - The Hidden Valley                                                        </a>
                                                    </li>
                                                                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/machu-picchu-via-inca-trail" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Machu Picchu via Inca Trail - The Way of the Incas -->
Machu Picchu via Inca Trail - The Way of the Incas                                                        </a>
                                                    </li>
                                                                                                                                                </ul>
                                                                                        <ul class="zen-menu__sublist" data-parent-id="117">
                                                                                                    <li class="zen-menu__subitem">
                                                        <a href="/holidays/kilimanjaro-the-long-way" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Kilimanjaro The Long Way -->
Kilimanjaro The Long Way                                                        </a>
                                                    </li>
                                                                                                                                                                                                </ul>
                                                                                        <ul class="zen-menu__sublist" data-parent-id="118">
                                                                                                    <li class="zen-menu__subitem">
                                                        <a href="/holidays/mt-toubkal-roof-of-the-north-weekender" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Toubkal Roof Of The North Weekender -->
Toubkal Roof Of The North Weekender                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/mt-toubkal-roof-of-the-north-8-day-trek" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Toubkal Roof Of The North 8 Day Trek -->
Toubkal Roof Of The North 8 Day Trek                                                        </a>
                                                    </li>
                                                                                                                                                                                                </ul>
                                                                                        <ul class="zen-menu__sublist" data-parent-id="659">
                                                                                                    <li class="zen-menu__subitem">
                                                        <a href="/holidays/tour-du-mont-blanc-trek" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Tour du Mont Blanc Trek -->
Tour du Mont Blanc Trek                                                        </a>
                                                    </li>
                                                                                                                                                </ul>
                                                                                        <ul class="zen-menu__sublist" data-parent-id="764">
                                                                                                    <li class="zen-menu__subitem">
                                                        <a href="/holidays/mt-olympus-myths-and-legends-weekender" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Mount Olympus Weekender -->
Mount Olympus Weekender                                                        </a>
                                                    </li>
                                                                                                                                                </ul>
                                                                                        <ul class="zen-menu__sublist" data-parent-id="470">
                                                                                                    <li class="zen-menu__subitem">
                                                        <a href="/holidays/tour-du-mont-blanc-trek" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Tour du Mont Blanc Trek -->
Tour du Mont Blanc Trek                                                        </a>
                                                    </li>
                                                                                                                                                </ul>
                                                                                        <ul class="zen-menu__sublist" data-parent-id="504">
                                                                                                    <li class="zen-menu__subitem">
                                                        <a href="/holidays/uk-training-weekend" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Brecon Training Weekend -->
Brecon Training Weekend                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="https://evertrek.co.uk/holidays/north-wales-training-weekend" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Snowdonia Training Weekend -->
Snowdonia Training Weekend                                                        </a>
                                                    </li>
                                                                                                                                                        <li class="zen-menu__subitem">
                                                        <a href="/holidays/scotland-winter-skills-weekend" class="zen-menu__sublink zen-menu__sublink--trip">
                                                            <!-- Processing title: Scotland Winter Skills -->
Scotland Winter Skills                                                        </a>
                                                    </li>
                                                                                                                                                </ul>
                                                                        </div>
                        </div>
                        
                                            </div>
                </div>
                    </li>
                    <li class="zen-menu__item" data-item-id="605">
            <!-- Processing title: Collections -->
<!-- Forcing empty href for Collections (ID: 605) -->
<!-- Using span for non-clickable item: Collections (ID: 605) -->
<span class="zen-menu__link zen-menu__link--has-children" >Collections<span class="zen-menu__arrow"><svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1060_3236)">
<path d="M16.59 9.09009L12 13.6701L7.41 9.09009L6 10.5001L12 16.5001L18 10.5001L16.59 9.09009Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_1060_3236">
<rect width="24" height="24" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
</span></span>
                            <!-- Dropdown Panel -->
                <div class="zen-menu__panel" data-parent-id="605">
                    <div class="zen-menu__columns">
                        <div class="zen-menu__column">
                            <!-- 2nd level -->
                            <ul class="zen-menu__sublist">
                                                                    <li class="zen-menu__subitem" data-item-id="606">
                                        <!-- Removing href for Collections item: Best Sellers (ID: 606) -->
<!-- Processing title: Best Sellers -->
<!-- Forcing empty href for Collections item: Best Sellers (ID: 606) -->
<!-- Forcing empty href for Collections first column item: Best Sellers (ID: 606) -->
<!-- Using span for non-clickable item: Best Sellers (ID: 606) -->
<span class="zen-menu__sublink zen-menu__sublink--collection" ><img src="/templates/zenbase/icons/adv-bestsellers.svg" alt="" class="zen-menu__icon" />Best Sellers</span>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="775">
                                        <!-- Removing href for Collections item: Bucket List Adventures (ID: 775) -->
<!-- Processing title: Bucket List Adventures -->
<!-- Forcing empty href for Collections item: Bucket List Adventures (ID: 775) -->
<!-- Forcing empty href for Collections first column item: Bucket List Adventures (ID: 775) -->
<!-- Using span for non-clickable item: Bucket List Adventures (ID: 775) -->
<span class="zen-menu__sublink zen-menu__sublink--collection" ><img src="/templates/zenbase/icons/cat-bucket-list.svg" alt="" class="zen-menu__icon" />Bucket List Adventures</span>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="776">
                                        <!-- Removing href for Collections item: Short Trips (ID: 776) -->
<!-- Processing title: Short Trips -->
<!-- Forcing empty href for Collections item: Short Trips (ID: 776) -->
<!-- Forcing empty href for Collections first column item: Short Trips (ID: 776) -->
<!-- Using span for non-clickable item: Short Trips (ID: 776) -->
<span class="zen-menu__sublink zen-menu__sublink--collection" ><img src="/templates/zenbase/icons/cat-beginner.svg" alt="" class="zen-menu__icon" />Short Trips</span>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="610">
                                        <!-- Removing href for Collections item: Training Weekends (ID: 610) -->
<!-- Processing title: Training Weekends -->
<!-- Forcing empty href for Collections item: Training Weekends (ID: 610) -->
<!-- Forcing empty href for Collections first column item: Training Weekends (ID: 610) -->
<!-- Using span for non-clickable item: Training Weekends (ID: 610) -->
<span class="zen-menu__sublink zen-menu__sublink--collection" ><img src="/templates/zenbase/icons/adv-training.svg" alt="" class="zen-menu__icon" />Training Weekends</span>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="608">
                                        <!-- Removing href for Collections item: Epic Peaks (ID: 608) -->
<!-- Processing title: Epic Peaks -->
<!-- Forcing empty href for Collections item: Epic Peaks (ID: 608) -->
<!-- Forcing empty href for Collections first column item: Epic Peaks (ID: 608) -->
<!-- Using span for non-clickable item: Epic Peaks (ID: 608) -->
<span class="zen-menu__sublink zen-menu__sublink--collection" ><img src="/templates/zenbase/icons/adv-peaks.svg" alt="" class="zen-menu__icon" />Epic Peaks</span>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="609">
                                        <!-- Removing href for Collections item: Remote Adventures (ID: 609) -->
<!-- Processing title: Remote Adventures -->
<!-- Forcing empty href for Collections item: Remote Adventures (ID: 609) -->
<!-- Forcing empty href for Collections first column item: Remote Adventures (ID: 609) -->
<!-- Using span for non-clickable item: Remote Adventures (ID: 609) -->
<span class="zen-menu__sublink zen-menu__sublink--collection" ><img src="/templates/zenbase/icons/adv-remote.svg" alt="" class="zen-menu__icon" />Remote Adventures</span>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="777">
                                        <!-- Removing href for Collections item: Hardcore Challenges (ID: 777) -->
<!-- Processing title: Hardcore Challenges -->
<!-- Forcing empty href for Collections item: Hardcore Challenges (ID: 777) -->
<!-- Forcing empty href for Collections first column item: Hardcore Challenges (ID: 777) -->
<!-- Using span for non-clickable item: Hardcore Challenges (ID: 777) -->
<span class="zen-menu__sublink zen-menu__sublink--collection" ><img src="/templates/zenbase/icons/cat-hardcore-challenges.svg" alt="" class="zen-menu__icon" />Hardcore Challenges</span>                                    </li>
                                                            </ul>
                        </div>

                                                    <div class="zen-menu__column">
                                <!-- 3rd level -->
                                <div class="zen-menu__third-level">
                                                                                                                        <ul class="zen-menu__sublist" data-parent-id="606">
                                                <!-- Collection: Best Sellers (ID: 606) has 7 trips -->
                                                    <li class="zen-menu__subitem" data-item-id="780">
                                                        <a href="https://evertrek.co.uk/holidays/everest-base-camp-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: Everest Base Camp Trek -->
Everest Base Camp Trek                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="612">
                                                        <a href="https://evertrek.co.uk/holidays/kilimanjaro-the-long-way" class="zen-menu__sublink">
                                                            <!-- Processing title: Kilimanjaro The Long Way -->
Kilimanjaro The Long Way                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="613">
                                                        <a href="https://evertrek.co.uk/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley" class="zen-menu__sublink">
                                                            <!-- Processing title: Machu Picchu via Tomacaya -->
Machu Picchu via Tomacaya                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="779">
                                                        <a href="https://evertrek.co.uk/holidays/mt-toubkal-roof-of-the-north-weekender" class="zen-menu__sublink">
                                                            <!-- Processing title: Toubkal Roof of the North Weekender -->
Toubkal Roof of the North Weekender                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="778">
                                                        <a href="https://evertrek.co.uk/holidays/tour-du-mont-blanc-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: Tour du Mont Blanc Trek -->
Tour du Mont Blanc Trek                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="611">
                                                        <a href="https://evertrek.co.uk/holidays/everest-base-camp-via-gokyo-valley" class="zen-menu__sublink">
                                                            <!-- Processing title: Everest Base Camp via Gokyo Valley -->
Everest Base Camp via Gokyo Valley                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="781">
                                                        <a href="https://evertrek.co.uk/holidays/ultimate-island-peak-and-everest-base-camp-expedition" class="zen-menu__sublink">
                                                            <!-- Processing title: Ultimate Island Peak and Everest Base Camp -->
Ultimate Island Peak and Everest Base Camp                                                        </a>
                                                    </li>
                                                                                            </ul>
                                                                                                                                                                <ul class="zen-menu__sublist" data-parent-id="775">
                                                <!-- Collection: Bucket List Adventures (ID: 775) has 5 trips -->
                                                    <li class="zen-menu__subitem" data-item-id="788">
                                                        <a href="https://evertrek.co.uk/holidays/kilimanjaro-the-long-way" class="zen-menu__sublink">
                                                            <!-- Processing title: Kilimanjaro The Long Way -->
Kilimanjaro The Long Way                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="789">
                                                        <a href="https://evertrek.co.uk/holidays/everest-base-camp-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: Everest Base Camp Trek -->
Everest Base Camp Trek                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="614">
                                                        <a href="https://evertrek.co.uk/holidays/machu-picchu-via-inca-trail" class="zen-menu__sublink">
                                                            <!-- Processing title: Machu Picchu via Inca Trail -->
Machu Picchu via Inca Trail                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="790">
                                                        <a href="https://evertrek.co.uk/holidays/tour-du-mont-blanc-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: Tour du Mont Blanc Trek -->
Tour du Mont Blanc Trek                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="791">
                                                        <a href="https://evertrek.co.uk/holidays/k2-base-camp-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: K2 Base Camp Trek -->
K2 Base Camp Trek                                                        </a>
                                                    </li>
                                                                                            </ul>
                                                                                                                                                                <ul class="zen-menu__sublist" data-parent-id="776">
                                                <!-- Collection: Short Trips (ID: 776) has 5 trips -->
                                                    <li class="zen-menu__subitem" data-item-id="792">
                                                        <a href="https://evertrek.co.uk/holidays/mt-toubkal-roof-of-the-north-weekender" class="zen-menu__sublink">
                                                            <!-- Processing title: Toubkal Roof of the North Weekender -->
Toubkal Roof of the North Weekender                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="793">
                                                        <a href="https://evertrek.co.uk/holidays/mt-olympus-myths-and-legends-weekender" class="zen-menu__sublink">
                                                            <!-- Processing title: Mount Olympus Weekender -->
Mount Olympus Weekender                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="794">
                                                        <a href="https://evertrek.co.uk/holidays/uk-training-weekend" class="zen-menu__sublink">
                                                            <!-- Processing title: Brecon Beacons Training Weekend -->
Brecon Beacons Training Weekend                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="795">
                                                        <a href="https://evertrek.co.uk/holidays/scotland-winter-skills-weekend" class="zen-menu__sublink">
                                                            <!-- Processing title: Scottish Winter Skills -->
Scottish Winter Skills                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="796">
                                                        <a href="https://evertrek.co.uk/holidays/north-wales-training-weekend" class="zen-menu__sublink">
                                                            <!-- Processing title: Snowdonia Training Weekend -->
Snowdonia Training Weekend                                                        </a>
                                                    </li>
                                                                                            </ul>
                                                                                                                                                                <ul class="zen-menu__sublist" data-parent-id="610">
                                                <!-- Collection: Training Weekends (ID: 610) has 3 trips -->
                                                    <li class="zen-menu__subitem" data-item-id="632">
                                                        <a href="https://evertrek.co.uk/holidays/uk-training-weekend" class="zen-menu__sublink">
                                                            <!-- Processing title: Brecon Beacons Training Weekend -->
Brecon Beacons Training Weekend                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="633">
                                                        <a href="https://evertrek.co.uk/holidays/scotland-winter-skills-weekend" class="zen-menu__sublink">
                                                            <!-- Processing title: Scottish Winter Skills -->
Scottish Winter Skills                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="634">
                                                        <a href="https://evertrek.co.uk/holidays/north-wales-training-weekend" class="zen-menu__sublink">
                                                            <!-- Processing title: Snowdonia Training Weekend -->
Snowdonia Training Weekend                                                        </a>
                                                    </li>
                                                                                            </ul>
                                                                                                                                                                <ul class="zen-menu__sublist" data-parent-id="608">
                                                <!-- Collection: Epic Peaks (ID: 608) has 6 trips -->
                                                    <li class="zen-menu__subitem" data-item-id="782">
                                                        <a href="https://evertrek.co.uk/holidays/kilimanjaro-the-long-way" class="zen-menu__sublink">
                                                            <!-- Processing title: Kilimanjaro The Long Way -->
Kilimanjaro The Long Way                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="622">
                                                        <a href="https://evertrek.co.uk/holidays/ultimate-island-peak-and-everest-base-camp-expedition" class="zen-menu__sublink">
                                                            <!-- Processing title: Ultimate Island Peak &amp; Everest Base Camp -->
Ultimate Island Peak & Everest Base Camp                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="620">
                                                        <a href="https://evertrek.co.uk/holidays/ultimate-mera-peak-expedition" class="zen-menu__sublink">
                                                            <!-- Processing title: Ultimate Mera Peak -->
Ultimate Mera Peak                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="619">
                                                        <a href="https://evertrek.co.uk/holidays/island-peak-expedition" class="zen-menu__sublink">
                                                            <!-- Processing title: Island Peak Expedition -->
Island Peak Expedition                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="783">
                                                        <a href="https://evertrek.co.uk/holidays/mt-toubkal-roof-of-the-north-weekender" class="zen-menu__sublink">
                                                            <!-- Processing title: Toubkal Roof of the North Weekender -->
Toubkal Roof of the North Weekender                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="784">
                                                        <a href="https://evertrek.co.uk/holidays/mt-olympus-myths-and-legends-weekender" class="zen-menu__sublink">
                                                            <!-- Processing title: Mount Olympus Weekender -->
Mount Olympus Weekender                                                        </a>
                                                    </li>
                                                                                            </ul>
                                                                                                                                                                <ul class="zen-menu__sublist" data-parent-id="609">
                                                <!-- Collection: Remote Adventures (ID: 609) has 5 trips -->
                                                    <li class="zen-menu__subitem" data-item-id="626">
                                                        <a href="https://evertrek.co.uk/holidays/k2-base-camp-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: K2 Base Camp -->
K2 Base Camp                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="628">
                                                        <a href="https://evertrek.co.uk/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley" class="zen-menu__sublink">
                                                            <!-- Processing title: Machu Picchu via Tomacaya -->
Machu Picchu via Tomacaya                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="629">
                                                        <a href="https://evertrek.co.uk/holidays/annapurna-circuit-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: Annapurna Circuit -->
Annapurna Circuit                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="630">
                                                        <a href="https://evertrek.co.uk/holidays/everest-three-passes-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: Everest Three Passes Trek -->
Everest Three Passes Trek                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="631">
                                                        <a href="https://evertrek.co.uk/holidays/langtang-valley-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: Langtang Valley -->
Langtang Valley                                                        </a>
                                                    </li>
                                                                                            </ul>
                                                                                                                                                                <ul class="zen-menu__sublist" data-parent-id="777">
                                                <!-- Collection: Hardcore Challenges (ID: 777) has 3 trips -->
                                                    <li class="zen-menu__subitem" data-item-id="785">
                                                        <a href="https://evertrek.co.uk/holidays/k2-base-camp-trek" class="zen-menu__sublink">
                                                            <!-- Processing title: K2 Base Camp Trek -->
K2 Base Camp Trek                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="786">
                                                        <a href="https://evertrek.co.uk/holidays/ultimate-island-peak-and-everest-base-camp-expedition" class="zen-menu__sublink">
                                                            <!-- Processing title: Ultimate Island Peak and Everest Base Camp -->
Ultimate Island Peak and Everest Base Camp                                                        </a>
                                                    </li>
                                                                                                    <li class="zen-menu__subitem" data-item-id="787">
                                                        <a href="https://evertrek.co.uk/holidays/ultimate-mera-peak-expedition" class="zen-menu__sublink">
                                                            <!-- Processing title: Ultimate Mera Peak -->
Ultimate Mera Peak                                                        </a>
                                                    </li>
                                                                                            </ul>
                                                                                                            </div>
                            </div>
                        
                        
                                                <!-- For Collections menu, we add a hidden third column to maintain the structure -->
                        <div class="zen-menu__column" style="display:none;">
                            <!-- This is a placeholder to maintain the structure, but we don't need a third column for Collections -->
                        </div>
                                            </div>
                </div>
                    </li>
                    <li class="zen-menu__item" data-item-id="131">
            <!-- Processing title: Knowledge Centre -->
<!-- Using anchor for clickable item: Knowledge Centre (ID: 131) with href: /knowledge-centre -->
<a class="zen-menu__link" href="/knowledge-centre" >Knowledge Centre</a>
                    </li>
                    <li class="zen-menu__item" data-item-id="362">
            <!-- Processing title: About Us -->
<!-- Forced URL for About Us menu item: /about-us/about-us -->
<!-- Using anchor for clickable item: About Us (ID: 362) with href: /about-us/about-us -->
<a class="zen-menu__link zen-menu__link--has-children" href="/about-us/about-us" >About Us</a>
                            <!-- Dropdown Panel -->
                <div class="zen-menu__panel" data-parent-id="362">
                    <div class="zen-menu__columns">
                        <div class="zen-menu__column">
                            <!-- 2nd level -->
                            <ul class="zen-menu__sublist">
                                                                    <li class="zen-menu__subitem" data-item-id="586">
                                        <!-- Processing title: About Us -->
<!-- Forced URL for About Us menu item: /about-us -->
<!-- Using anchor for clickable item: About Us (ID: 586) with href: /about-us -->
<a class="zen-menu__sublink" href="/about-us" >About Us</a>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="598">
                                        <!-- Processing title: Download our Adventure Brochure -->
<!-- Forced URL for About Us menu item: /about-us/download-our-adventure-brochure -->
<!-- Using anchor for clickable item: Download our Adventure Brochure (ID: 598) with href: /about-us/download-our-adventure-brochure -->
<a class="zen-menu__sublink" href="/about-us/download-our-adventure-brochure" >Download our Adventure Brochure</a>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="494">
                                        <!-- Processing title: Reasons to choose EverTrek -->
<!-- Forced URL for About Us menu item: /about-us/reasons-to-choose-evertrek -->
<!-- Using anchor for clickable item: Reasons to choose EverTrek (ID: 494) with href: /about-us/reasons-to-choose-evertrek -->
<a class="zen-menu__sublink" href="/about-us/reasons-to-choose-evertrek" >Reasons to choose EverTrek</a>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="535">
                                        <!-- Processing title: Refer and Earn - EverTrekker Rewards  -->
<!-- Forced URL for About Us menu item: /evertrekker-rewards -->
<!-- Using anchor for clickable item: Refer and Earn - EverTrekker Rewards  (ID: 535) with href: /evertrekker-rewards -->
<a class="zen-menu__sublink" href="/evertrekker-rewards" >Refer and Earn - EverTrekker Rewards </a>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="490">
                                        <!-- Processing title: EverTrek Partnerships -->
<!-- Forced URL for About Us menu item: /about-us/evertrek-partnerships -->
<!-- Using anchor for clickable item: EverTrek Partnerships (ID: 490) with href: /about-us/evertrek-partnerships -->
<a class="zen-menu__sublink" href="/about-us/evertrek-partnerships" >EverTrek Partnerships</a>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="492">
                                        <!-- Processing title: Charity Projects -->
<!-- Forced URL for About Us menu item: /about-us/charity-projects -->
<!-- Using anchor for clickable item: Charity Projects (ID: 492) with href: /about-us/charity-projects -->
<a class="zen-menu__sublink" href="/about-us/charity-projects" >Charity Projects</a>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="488">
                                        <!-- Processing title: Responsible Travel -->
<!-- Forced URL for About Us menu item: /about-us/responsible-travel -->
<!-- Using anchor for clickable item: Responsible Travel (ID: 488) with href: /about-us/responsible-travel -->
<a class="zen-menu__sublink" href="/about-us/responsible-travel" >Responsible Travel</a>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="489">
                                        <!-- Processing title: EverTrek Reviews -->
<!-- Forced URL for About Us menu item: /about-us/evertrek-reviews -->
<!-- Using anchor for clickable item: EverTrek Reviews (ID: 489) with href: /about-us/evertrek-reviews -->
<a class="zen-menu__sublink" href="/about-us/evertrek-reviews" >EverTrek Reviews</a>                                    </li>
                                                                    <li class="zen-menu__subitem" data-item-id="587">
                                        <!-- Processing title: YETI-AI Ask us Anything -->
<!-- Forced URL for About Us menu item: /about-us/ask-us-anything-with-yet-ai-bot -->
<!-- Using anchor for clickable item: YETI-AI Ask us Anything (ID: 587) with href: /about-us/ask-us-anything-with-yet-ai-bot -->
<a class="zen-menu__sublink" href="/about-us/ask-us-anything-with-yet-ai-bot" >YETI-AI Ask us Anything</a>                                    </li>
                                                            </ul>
                        </div>

                        
                        
                                            </div>
                </div>
                    </li>
                    <li class="zen-menu__item" data-item-id="284">
            <!-- Processing title: Contact -->
<!-- Using anchor for clickable item: Contact (ID: 284) with href: /contact -->
<a class="zen-menu__link" href="/contact" >Contact</a>
                    </li>
                    <li class="zen-menu__item" data-item-id="727">
            <!-- Processing title: Search -->
<!-- Using anchor for clickable item: Search (ID: 727) with href: /holidays#/holidays?sort=ordering%7CASC -->
<a class="zen-menu__link" href="/holidays#/holidays?sort=ordering%7CASC" ><span class="zen-menu__icon"><svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1060_3250)">
<path d="M12.9167 12.1667H12.2583L12.025 11.9417C12.8417 10.9917 13.3333 9.75833 13.3333 8.41667C13.3333 5.425 10.9083 3 7.91667 3C4.925 3 2.5 5.425 2.5 8.41667C2.5 11.4083 4.925 13.8333 7.91667 13.8333C9.25833 13.8333 10.4917 13.3417 11.4417 12.525L11.6667 12.7583V13.4167L15.8333 17.575L17.075 16.3333L12.9167 12.1667ZM7.91667 12.1667C5.84167 12.1667 4.16667 10.4917 4.16667 8.41667C4.16667 6.34167 5.84167 4.66667 7.91667 4.66667C9.99167 4.66667 11.6667 6.34167 11.6667 8.41667C11.6667 10.4917 9.99167 12.1667 7.91667 12.1667Z" fill="black"/>
</g>
<defs>
<clipPath id="clip0_1060_3250">
<rect width="20" height="20" fill="white" transform="translate(0 0.5)"/>
</clipPath>
</defs>
</svg>
</span></a>
                    </li>
        </ul>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Track active items
    let activeSecondLevelItem = null;
    let activeThirdLevelItem = null;

    // Initialize the menu state
    function initializeMenu() {
        // Hide all third level menus by default
        document.querySelectorAll('.zen-menu__third-level > .zen-menu__sublist').forEach(ul => {
            ul.style.display = 'none';
        });

        // Show the first third level menu for each dropdown
        document.querySelectorAll('.zen-menu__third-level').forEach(container => {
            const firstMenu = container.querySelector('.zen-menu__sublist:first-child');
            if (firstMenu) {
                firstMenu.style.display = 'block';
            }
        });

        // Hide all fourth level menus by default
        document.querySelectorAll('.zen-menu__fourth-level > .zen-menu__sublist').forEach(ul => {
            ul.style.display = 'none';
        });

        // Clear active states
        if (activeSecondLevelItem) {
            activeSecondLevelItem.classList.remove('active');
            activeSecondLevelItem = null;
        }

        if (activeThirdLevelItem) {
            activeThirdLevelItem.classList.remove('active');
            activeThirdLevelItem = null;
        }
    }

    // Initialize the menu
    initializeMenu();

    // Get all second level items
    const secondLevelItems = document.querySelectorAll('.zen-menu__column:first-child .zen-menu__subitem');

    // Add event listeners for second level items
    secondLevelItems.forEach(item => {
        const itemId = item.getAttribute('data-item-id');
        const itemLink = item.querySelector('.zen-menu__sublink');

        if (itemId && itemLink) {
            // Add hover event listeners
            item.addEventListener('mouseenter', () => {
                // Clear previous active state
                if (activeSecondLevelItem && activeSecondLevelItem !== itemLink) {
                    activeSecondLevelItem.classList.remove('active');
                }

                // Set new active state
                itemLink.classList.add('active');
                activeSecondLevelItem = itemLink;

                // Hide all third level menus
                document.querySelectorAll('.zen-menu__third-level > .zen-menu__sublist').forEach(ul => {
                    ul.style.display = 'none';
                });

                // Show only the corresponding third level menu
                const thirdLevelMenu = document.querySelector(`.zen-menu__third-level > .zen-menu__sublist[data-parent-id="${itemId}"]`);
                if (thirdLevelMenu) {
                    thirdLevelMenu.style.display = 'block';
                }

                // Hide all fourth level menus
                document.querySelectorAll('.zen-menu__fourth-level > .zen-menu__sublist').forEach(ul => {
                    ul.style.display = 'none';
                });

                // Clear third level active state
                if (activeThirdLevelItem) {
                    activeThirdLevelItem.classList.remove('active');
                    activeThirdLevelItem = null;
                }
            });
        }
    });

    // Add event listeners for third level items
    const thirdLevelItems = document.querySelectorAll('.zen-menu__third-level .zen-menu__subitem');

    thirdLevelItems.forEach(item => {
        const itemId = item.getAttribute('data-item-id');
        const itemLink = item.querySelector('.zen-menu__sublink');

        if (itemId && itemLink) {
            // Add hover event listeners
            item.addEventListener('mouseenter', () => {
                // Clear previous active state
                if (activeThirdLevelItem && activeThirdLevelItem !== itemLink) {
                    activeThirdLevelItem.classList.remove('active');
                }

                // Set new active state
                itemLink.classList.add('active');
                activeThirdLevelItem = itemLink;

                // Hide all fourth level menus
                document.querySelectorAll('.zen-menu__fourth-level > .zen-menu__sublist').forEach(ul => {
                    ul.style.display = 'none';
                });

                // Show only the corresponding fourth level menu
                const fourthLevelMenu = document.querySelector(`.zen-menu__fourth-level > .zen-menu__sublist[data-parent-id="${itemId}"]`);
                if (fourthLevelMenu) {
                    fourthLevelMenu.style.display = 'block';
                }
            });
        }
    });

    // Add event listeners for menu items with dropdowns
    const menuItems = document.querySelectorAll('.zen-menu__item');

    menuItems.forEach(item => {
        const panel = item.querySelector('.zen-menu__panel');

        if (panel) {
            // Show panel when hovering over menu item
            item.addEventListener('mouseenter', () => {
                // Initialize the third level menus when opening the panel
                const thirdLevelContainer = panel.querySelector('.zen-menu__third-level');
                if (thirdLevelContainer) {
                    // Hide all third level menus
                    document.querySelectorAll('.zen-menu__third-level > .zen-menu__sublist').forEach(ul => {
                        ul.style.display = 'none';
                    });

                    // Show the first third level menu
                    const firstMenu = thirdLevelContainer.querySelector('.zen-menu__sublist:first-child');
                    if (firstMenu) {
                        firstMenu.style.display = 'block';

                        // Set the first second level item as active
                        const firstSecondLevelItem = panel.querySelector('.zen-menu__column:first-child .zen-menu__subitem:first-child .zen-menu__sublink');
                        if (firstSecondLevelItem) {
                            firstSecondLevelItem.classList.add('active');
                            activeSecondLevelItem = firstSecondLevelItem;
                        }
                    }
                }

                // Hide all fourth level menus
                document.querySelectorAll('.zen-menu__fourth-level > .zen-menu__sublist').forEach(ul => {
                    ul.style.display = 'none';
                });
            });

            // Reset state when mouse leaves the menu item
            item.addEventListener('mouseleave', (e) => {
                if (!panel.contains(e.relatedTarget)) {
                    // Reset menu state
                    initializeMenu();
                }
            });
        }
    });
});
</script>
                        </div>
                                          </div>
                  </div>
                </div>
                <div class="zen-header__info col-md-2 pe-0"> <!-- col-6 col-lg-2 ps-0 -->
                <div class="zen-header__search d-block d-xl-none">
                  <a class="zen-link zen-link--no-underline" href="#" data-bs-toggle="offcanvas" data-bs-target="#searchOffcanvas">
                    <!-- <i class="zen-icon zen-icon--text-sm fontello icon-sys-search me-3 me-xl-3"></i> -->
                  </a>
                </div>

                <!-- Offcanvas for keyword search -->
                <div class="offcanvas offcanvas-end" tabindex="-1" id="searchOffcanvas">
                  <div class="offcanvas-header">
                    <h5 class="offcanvas-title">Search</h5>
                    <button type="button" class="btn-close text-reset" data-bs-dismiss="offcanvas" aria-label="Close"></button>
                  </div>
                  <div class="offcanvas-body">
                    <form id="offcanvasSearchForm">
                      <div class="mb-3">
                        <input type="text" class="form-control" id="offcanvasSearchInput" placeholder="Enter search text">
                      </div>
                      <button type="submit" class="zen-btn zen-btn--primary zen-btn--full-size">Search</button>
                    </form>
                  </div>
                </div>

                  <div class="zen-flex-end d-flex">
                    <a class="zen-cta zen-cta--text-xl py-1 px-1" id="mobile-menu-trigger">
                      <i class="zen-icon zen-icon--text-lg fontello icon-sys-hamburger" aria-hidden="true"></i>
                    </a>
                  </div>
                </div>
              </div>
            </div>
            </div>
          </div>
          <span class="sticky-marker"></span>
        </header>
      </div>

      <!------------------------------------------------------------------------------
      // Body Content - Including Breadcrumbs
      //----------------------------------------------------------------------------->
      <div class="zen-body">
                                          <div class="zen-body__main pt-0">
          <main id="content" role="main" class="">
            <div id="system-message-container">
	</div>

            <div id="sp-page-builder" class="sp-page-builder homepage page-273">

	
	<div class="page-content">
				<section id="section-id-1739546099254" class="sppb-section home-hero sppb-hidden-xs" ><div class="sppb-row-container"><div class="sppb-row"><div class="sppb-col-md-5" id="column-wrap-id-1739546099251"><div id="column-id-1739546099251" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739546099257" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099257" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><h1>Epic Adventures, Expertly Guided</h1></div></div></div></div><div id="sppb-addon-wrapper-1739546099266" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099266" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><a class="btn btn-primary" href="/holidays#/holidays?sort=ordering%7CASC">Search all adventures <img src="/templates/zenbase/icons/search-small.svg" alt="" /></a> <a class="btn btn-primary btn-outline" href="/about-us/download-our-adventure-brochure" target="_blank" rel="noopener noreferrer">Download brochure <img src="/templates/zenbase/icons/download.svg" alt="" /></a></div></div></div></div><div id="sppb-addon-wrapper-1739546099263" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099263" class="sppb-hidden-xs clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><p><a href="#" data-bs-toggle="modal" data-bs-target="#videoLightbox"><img src="/templates/zenbase/icons/play.svg" alt="" /> Why choose us?</a></p></div></div></div></div><div id="sppb-addon-wrapper-1739546099283" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099283" class="sppb-hidden-xs clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-left "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/images/awards/awardsx3-black.svg"  alt="Image" title=""   /></div></div></div></div></div></div></div></div><div class="sppb-col-md-7" id="column-wrap-id-1739546099252"><div id="column-id-1739546099252" class="sppb-column" ><div class="sppb-column-addons"></div></div></div></div></div></section><section id="section-id-1743585701437" class="sppb-section home-hero sppb-hidden-md sppb-hidden-lg sppb-hidden-sm" ><div class="sppb-row-container"><div class="sppb-row"><div class="sppb-col-md-1" id="column-wrap-id-1743585701438"><div id="column-id-1743585701438" class="sppb-column" ><div class="sppb-column-addons"></div></div></div><div class="sppb-col-md-5" id="column-wrap-id-1743585701439"><div id="column-id-1743585701439" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743585701440" class="sppb-addon-wrapper"><div id="sppb-addon-1743585701440" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><h1>Epic Adventures, Expertly Guided</h1></div></div></div></div><div id="sppb-addon-wrapper-1743585701441" class="sppb-addon-wrapper"><div id="sppb-addon-1743585701441" class="sppb-hidden-md sppb-hidden-lg sppb-hidden-sm clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><p><a href="#" data-bs-toggle="modal" data-bs-target="#videoLightbox"><img src="/templates/zenbase/icons/play.svg" alt="" /> Why choose us?</a></p></div></div></div></div><div id="sppb-addon-wrapper-1743585701442" class="sppb-addon-wrapper"><div id="sppb-addon-1743585701442" class="sppb-hidden-md sppb-hidden-lg sppb-hidden-sm clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><p><img src="/templates/zenbase/images/trustpilot_rating.svg" alt="" /> Excellent</p></div></div></div></div><div id="sppb-addon-wrapper-1743585701443" class="sppb-addon-wrapper"><div id="sppb-addon-1743585701443" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><a class="btn btn-primary" href="/holidays#/holidays?sort=ordering%7CASC">Search all adventures <img src="/templates/zenbase/icons/search-small.svg" alt="" /></a> <a class="btn btn-primary btn-outline" href="/about-us/download-our-adventure-brochure">Download brochure <img src="/templates/zenbase/icons/download.svg" alt="" /></a></div></div></div></div></div></div></div><div class="sppb-col-md-6" id="column-wrap-id-1743585701446"><div id="column-id-1743585701446" class="sppb-column" ><div class="sppb-column-addons"></div></div></div></div></div></section><div id="section-id-1733150149652" class="sppb-section" ><div class="sppb-container-inner"><div class="sppb-row"><div class="sppb-col-md-12" id="column-wrap-id-1733150149657"><div id="column-id-1733150149657" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1736167383821" class="sppb-addon-wrapper"><div id="sppb-addon-1736167383821" class="clearfix "     ><div class="sppb-addon sppb-addon-holiday-tabs"><div class="sppb-addon-content"><div class="sppb-tabs" data-tabs-animation="true"><div class="sppb-nav-tabs-container"><div class="sppb-nav sppb-nav-tabs" role="tablist"><div class="sppb-tab active" role="tab" data-tab="55"><span class="sppb-tab-icon"><svg width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1475_17087)">
<path d="M14.0951 9.90394L13.4527 7.94483L15.1343 6.73387C15.4622 6.49784 15.593 6.09847 15.4676 5.71656C15.3424 5.33465 14.9996 5.08784 14.5945 5.08784H12.5161L11.8739 3.12872C11.7484 2.74682 11.4058 2.5 11.0005 2.5C10.5952 2.5 10.2526 2.74682 10.1271 3.12872L9.48494 5.08784H7.40649C7.00116 5.08784 6.65856 5.33465 6.53312 5.71656C6.40795 6.09873 6.53883 6.49784 6.86666 6.73413L8.54808 7.94483L7.90589 9.90369C7.78071 10.2856 7.91159 10.685 8.23942 10.9213C8.40347 11.0391 8.59136 11.0982 8.77925 11.0982C8.96714 11.0982 9.15503 11.0391 9.31908 10.9213L11.0005 9.71055L12.6819 10.9213C13.0098 11.157 13.4332 11.157 13.7611 10.9213C14.0891 10.6852 14.2203 10.2859 14.0951 9.90394ZM10.8844 3.37271C10.9069 3.30363 10.9601 3.28899 11.0005 3.28899C11.0409 3.28899 11.0941 3.30363 11.1166 3.37271L11.8482 5.60433C11.9016 5.7669 12.0545 5.87682 12.2268 5.87682H14.5945C14.6679 5.87682 14.6979 5.92228 14.7106 5.96029C14.7231 5.99856 14.7256 6.05301 14.6663 6.09564L12.7506 7.47483C12.6112 7.57525 12.5529 7.75349 12.606 7.91607L13.3378 10.1474C13.3604 10.2165 13.3262 10.2594 13.2933 10.2828C13.2609 10.3067 13.2098 10.3257 13.15 10.2828L11.2345 8.90359C11.0951 8.80342 10.9059 8.80342 10.7665 8.90359L8.85104 10.2828C8.79117 10.3254 8.74038 10.3061 8.70746 10.2828C8.67481 10.2591 8.64034 10.2168 8.66315 10.1477L9.39475 7.91607C9.44788 7.75349 9.38957 7.57525 9.25014 7.47483L7.3347 6.09564C7.27535 6.05301 7.27795 5.99856 7.29039 5.96055C7.30283 5.92228 7.33315 5.87682 7.40649 5.87682H9.77416C9.9465 5.87682 10.0994 5.7669 10.1528 5.60433" fill="currentColor"/>
<path d="M7.81093 11.6627H5.84599L5.23904 9.81073C5.1136 9.42882 4.771 9.18201 4.36567 9.18201C3.96035 9.18201 3.61774 9.42882 3.49231 9.81073L2.8851 11.6627H0.920161C0.514837 11.6627 0.172229 11.9096 0.0467965 12.2915C-0.078377 12.6736 0.052498 13.0728 0.380334 13.309L1.97001 14.4535L1.3628 16.3057C1.23763 16.6877 1.36876 17.087 1.69686 17.3231C2.02469 17.5591 2.44816 17.5586 2.77599 17.3231L4.36567 16.1784L5.95535 17.3231C6.1194 17.4409 6.30729 17.5 6.49518 17.5C6.68307 17.5 6.87096 17.4409 7.03475 17.3231C7.36258 17.087 7.49346 16.6877 7.36828 16.3057L6.76133 14.4535L8.35075 13.309C8.67859 13.0728 8.80946 12.6734 8.68429 12.2915C8.55912 11.9096 8.21625 11.6627 7.81093 11.6627ZM7.88271 12.6706L6.05927 13.9835C5.91985 14.0839 5.86154 14.2621 5.91466 14.4245L6.61102 16.5495C6.63383 16.6186 6.59936 16.6612 6.56671 16.6848C6.53457 16.7085 6.483 16.7277 6.42339 16.6846L4.59969 15.3714C4.52998 15.3213 4.44783 15.2961 4.36567 15.2961C4.28352 15.2961 4.20137 15.3213 4.13165 15.3714L2.30795 16.6846C2.24835 16.7275 2.19678 16.7082 2.16464 16.6846C2.13173 16.6612 2.09752 16.6183 2.12006 16.5495L2.81668 14.4247C2.86981 14.2621 2.8115 14.0839 2.67207 13.9835L0.848374 12.6706C0.789026 12.6279 0.791618 12.5735 0.804058 12.5355C0.816497 12.4972 0.846819 12.4517 0.920161 12.4517H3.17432C3.34666 12.4517 3.49957 12.3418 3.55295 12.1792L4.24957 10.0547C4.27212 9.98563 4.32524 9.97099 4.36567 9.97099C4.4061 9.97099 4.45923 9.98563 4.48178 10.0547L5.17813 12.1792C5.23152 12.3416 5.38442 12.4517 5.55677 12.4517H7.81093C7.88427 12.4517 7.91459 12.4972 7.92703 12.5352C7.93973 12.5735 7.94206 12.6279 7.88271 12.6706Z" fill="currentColor"/>
<path d="M21.9528 12.2915C21.8277 11.9096 21.4848 11.6627 21.0795 11.6627H19.1145L18.5076 9.81073C18.3822 9.42882 18.0396 9.18201 17.6342 9.18201C17.2289 9.18201 16.8863 9.42882 16.7609 9.81073L16.1537 11.6627H14.1887C13.7834 11.6627 13.4408 11.9096 13.3154 12.2915C13.1902 12.6736 13.3211 13.0728 13.6489 13.309L15.2386 14.4535L14.6314 16.3057C14.5062 16.6877 14.6373 17.087 14.9654 17.3231C15.2932 17.5591 15.7167 17.5586 16.0445 17.3231L17.6342 16.1784L19.2239 17.3231C19.388 17.4409 19.5758 17.5 19.7637 17.5C19.9516 17.5 20.1395 17.4409 20.3033 17.3231C20.6311 17.087 20.762 16.6877 20.6368 16.3057L20.0299 14.4535L21.6193 13.309C21.9471 13.0728 22.078 12.6734 21.9528 12.2915ZM21.1513 12.6706L19.3278 13.9835C19.1884 14.0839 19.1301 14.2621 19.1832 14.4245L19.8796 16.5495C19.9024 16.6186 19.8679 16.6612 19.8353 16.6848C19.8031 16.7085 19.7516 16.7277 19.6919 16.6846L17.8682 15.3714C17.7985 15.3213 17.7164 15.2961 17.6342 15.2961C17.5521 15.2961 17.4699 15.3213 17.4002 15.3714L15.5765 16.6846C15.5169 16.7275 15.4653 16.7082 15.4332 16.6846C15.4003 16.6612 15.3661 16.6183 15.3886 16.5495L16.0852 14.4247C16.1384 14.2621 16.0801 14.0839 15.9406 13.9835L14.1169 12.6706C14.0576 12.6279 14.0602 12.5735 14.0726 12.5355C14.0851 12.4972 14.1154 12.4517 14.1887 12.4517H16.4429C16.6152 12.4517 16.7681 12.3418 16.8215 12.1792L17.5181 10.0547C17.5407 9.98563 17.5938 9.97099 17.6342 9.97099C17.6747 9.97099 17.7278 9.98563 17.7503 10.0547L18.4467 12.1792C18.5001 12.3416 18.653 12.4517 18.8253 12.4517H21.0795C21.1528 12.4517 21.1831 12.4972 21.1956 12.5352C21.2083 12.5735 21.2106 12.6279 21.1513 12.6706Z" fill="currentColor"/>
</g>
<defs>
<clipPath id="clip0_1475_17087">
<rect width="22" height="15" fill="currentColor" transform="translate(0 2.5)"/>
</clipPath>
</defs>
</svg>
</span><span class="sppb-tab-title">Best Sellers</span></div><div class="sppb-tab" role="tab" data-tab="41"><span class="sppb-tab-icon"><svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1060_2276)">
<path d="M26.9978 4.62856C26.4867 4.24684 25.7714 4.34573 25.371 4.85483L24.792 5.59012C23.0895 4.58126 21.1107 4 18.9991 4C12.6584 4 7.5 9.21796 7.5 15.6317C7.5 18.7066 8.68779 21.5046 10.6209 23.5866L10.0403 24.324C9.64046 24.8319 9.704 25.5578 10.1844 25.9767C12.1227 27.6681 14.4459 28.741 16.9241 29.1263V31.8022H13.2859C12.1419 31.8022 11.2109 32.7437 11.2109 33.9011C11.2109 35.0585 12.1419 36 13.2859 36H24.712C25.8563 36 26.7874 35.0585 26.7874 33.9011C26.7874 32.7437 25.8563 31.8022 24.712 31.8022H21.074V29.1266C27.3273 28.1564 32.1575 22.8304 32.4824 16.3426C32.7135 11.7377 30.6632 7.35878 26.9978 4.62856ZM31.2826 16.2809C30.9898 22.1277 26.6814 26.9412 21.074 27.8988C20.8755 27.9328 20.6751 27.9611 20.4734 27.9852C20.3078 28.005 20.1416 28.0229 19.974 28.036C19.9401 28.0386 19.9065 28.039 19.8727 28.0414C19.2857 28.0824 18.702 28.084 18.1254 28.0433C17.9243 28.0291 17.724 28.0101 17.5247 27.9861C17.3234 27.962 17.1232 27.9329 16.9241 27.8989C14.7367 27.5254 12.6893 26.5621 10.98 25.0811L11.4917 24.4313C13.0096 25.7582 14.8721 26.6901 16.9241 27.0701C17.1226 27.1069 17.3228 27.1383 17.5247 27.1646C17.7235 27.1904 17.9237 27.2111 18.1254 27.2265C18.4142 27.2485 18.7048 27.2635 18.9991 27.2635C19.2933 27.2635 19.584 27.2485 19.8727 27.2265C20.0744 27.2111 20.2747 27.1904 20.4734 27.1646C20.6753 27.1383 20.8755 27.1069 21.074 27.0701C26.4271 26.0787 30.4981 21.3287 30.4981 15.6317C30.4981 11.7904 28.6466 8.37943 25.8001 6.26009L26.3036 5.62065C29.6319 8.10918 31.4926 12.0922 31.2826 16.2809ZM25.586 33.9011C25.586 34.3885 25.1938 34.7848 24.712 34.7848H13.2859C12.8041 34.7848 12.4123 34.3885 12.4123 33.9011C12.4123 33.4138 12.8041 33.0174 13.2859 33.0174H24.712C25.1938 33.0174 25.586 33.4138 25.586 33.9011ZM18.1254 31.8022V29.2579C18.4147 29.2765 18.7049 29.2892 18.9969 29.2892C19.2874 29.2892 19.58 29.2736 19.8727 29.2548V31.8022H18.1254ZM18.9991 5.21519C20.8277 5.21519 22.5452 5.70201 24.0353 6.551C24.2115 6.6514 24.3843 6.75719 24.5539 6.86758C24.7229 6.9776 24.8889 7.09187 25.051 7.21143C27.6218 9.1072 29.2968 12.1746 29.2968 15.6317C29.2968 20.6566 25.7611 24.8619 21.074 25.8357C20.876 25.8769 20.6754 25.9104 20.4734 25.9398C20.275 25.9688 20.0745 25.9909 19.8727 26.0081C19.5844 26.0328 19.2936 26.0483 18.9991 26.0483C18.7046 26.0483 18.4137 26.0328 18.1254 26.0081C17.9236 25.9909 17.7231 25.9688 17.5247 25.9398C17.3227 25.9104 17.1221 25.8769 16.9241 25.8357C15.1556 25.4683 13.5529 24.6386 12.2403 23.4806C12.0889 23.3471 11.9409 23.2099 11.7975 23.0678C11.6533 22.9248 11.5118 22.7789 11.376 22.6276C9.71495 20.7773 8.70133 18.322 8.70133 15.6317C8.70133 9.88805 13.3209 5.21519 18.9991 5.21519Z" fill="currentColor"/>
<path d="M14.051 15.6495L15.7912 16.9284L15.7413 17.0837L15.212 18.7312L15.1264 18.9976C15.0358 19.2798 15.0364 19.5675 15.1137 19.8312C15.1976 20.1173 15.3723 20.3751 15.6295 20.5645C16.0185 20.8507 16.4963 20.9081 16.9239 20.7442C17.0399 20.6998 17.153 20.6423 17.2587 20.5649L17.5245 20.3695L18.1253 19.928L18.9989 19.286L19.8725 19.9279L20.4733 20.3692L20.7395 20.5649C20.845 20.6424 20.958 20.6998 21.0739 20.7443C21.2295 20.804 21.391 20.8374 21.5533 20.8374C21.8372 20.8374 22.1207 20.7464 22.3683 20.5645C22.8626 20.2009 23.06 19.5858 22.8712 18.9976L22.2068 16.9284L23.947 15.6495C24.4416 15.2856 24.6391 14.6709 24.4503 14.0823C24.261 13.494 23.744 13.1139 23.1324 13.1139H20.9812L20.8138 12.5924L20.5914 11.8996L20.369 11.2069L20.3168 11.0443C20.1275 10.4561 19.6105 10.0759 18.9989 10.0759C18.3875 10.0759 17.8701 10.4561 17.681 11.0443L17.0164 13.1139H14.8654C14.254 13.1139 13.7366 13.494 13.5477 14.0823C13.3587 14.6705 13.5564 15.2856 14.051 15.6495ZM18.0242 13.9094L18.8237 11.4201C18.8577 11.3137 18.9379 11.2911 18.9989 11.2911C19.0599 11.2911 19.1401 11.3137 19.1741 11.4201L19.4716 12.3465L19.6941 13.0392L19.9165 13.7318L19.9734 13.909C20.054 14.1594 20.2847 14.3291 20.5448 14.3291H23.1324C23.2431 14.3291 23.2888 14.3991 23.3076 14.458C23.3264 14.5166 23.3303 14.6005 23.2407 14.6661L21.1474 16.2045C20.937 16.3592 20.849 16.6337 20.9292 16.8837L21.7285 19.3734C21.7629 19.4798 21.7113 19.5451 21.6616 19.5815C21.6127 19.6179 21.5341 19.6475 21.4454 19.5815L21.0739 19.3084L20.4733 18.8671L19.352 18.0431C19.2466 17.966 19.1229 17.9272 18.9989 17.9272C18.8749 17.9272 18.7512 17.966 18.6458 18.0431L17.5245 18.8672L16.9239 19.3086L16.5526 19.5815C16.4633 19.6471 16.3853 19.6179 16.3358 19.5815C16.2865 19.5451 16.2349 19.4794 16.2691 19.3734L16.8167 17.6685L17.0686 16.8841C17.1187 16.7281 17.0968 16.5658 17.0273 16.4259C16.9852 16.3413 16.9298 16.2628 16.8504 16.2045L16.5865 16.0105L14.7573 14.6661C14.6677 14.6005 14.6715 14.5166 14.6902 14.4577C14.7092 14.3991 14.7547 14.3291 14.8654 14.3291H17.4529C17.7131 14.3291 17.9436 14.1594 18.0242 13.9094Z" fill="currentColor"/>
</g>
<defs>
<clipPath id="clip0_1060_2276">
<rect width="25" height="32" fill="white" transform="translate(7.5 4)"/>
</clipPath>
</defs>
</svg>
</span><span class="sppb-tab-title">Bucket List Adventures</span></div><div class="sppb-tab" role="tab" data-tab="56"><span class="sppb-tab-icon"><?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg width="100%" height="100%" viewBox="0 0 41 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" xmlns:serif="http://www.serif.com/" style="fill-rule:evenodd;clip-rule:evenodd;stroke-linejoin:round;stroke-miterlimit:2;">
    <path d="M6.899,21.719C6.883,21.63 6.874,21.539 6.874,21.446L6.874,18.594C6.874,17.756 7.559,17.071 8.393,17.071L9.973,17.071C10.171,17.071 10.361,17.11 10.536,17.18L10.536,11.517C10.139,10.924 9.906,10.212 9.906,9.447C9.906,7.397 11.577,5.721 13.619,5.721L15.718,5.721C15.49,5.379 15.356,4.968 15.356,4.528C15.356,3.343 16.322,2.375 17.502,2.375L23.231,2.375C24.41,2.375 25.377,3.343 25.377,4.528C25.377,4.968 25.243,5.379 25.014,5.721L27.098,5.721C29.139,5.721 30.81,7.397 30.81,9.447C30.81,10.212 30.578,10.924 30.181,11.517L30.181,17.367C30.433,17.181 30.743,17.071 31.077,17.071L32.658,17.071C33.492,17.071 34.176,17.756 34.176,18.594L34.176,21.446C34.176,21.54 34.167,21.632 34.151,21.722C34.943,21.77 35.577,22.435 35.577,23.241L35.577,31.046C35.577,31.884 34.892,32.568 34.058,32.567L30.59,32.567C30.582,32.567 30.573,32.567 30.564,32.567C30.723,32.981 30.81,33.43 30.81,33.899C30.81,35.949 29.139,37.625 27.098,37.625L13.619,37.625C11.577,37.625 9.906,35.949 9.906,33.899C9.906,33.43 9.993,32.981 10.152,32.567L6.799,32.567C5.965,32.567 5.281,31.883 5.281,31.045L5.281,23.241C5.281,22.403 5.965,21.719 6.799,21.719L6.899,21.719ZM10.536,31.046L10.536,23.241C10.536,23.092 10.416,22.969 10.267,22.969L9.973,22.968L8.393,22.968L6.799,22.969C6.651,22.969 6.531,23.092 6.531,23.241L6.531,31.045C6.531,31.194 6.651,31.318 6.799,31.318L10.268,31.318C10.416,31.318 10.536,31.194 10.536,31.046ZM28.931,30.661L28.931,12.685C28.389,12.996 27.763,13.174 27.098,13.174L25.847,13.174L25.848,13.849L25.825,14.069L25.761,14.276L25.66,14.462L25.527,14.623L25.366,14.757L25.18,14.859L24.973,14.924L24.752,14.947L24.53,14.924L24.324,14.859L24.137,14.757L23.976,14.624L23.843,14.462L23.742,14.276L23.678,14.069L23.656,13.849L23.656,13.174L17.548,13.174L17.548,13.849L17.526,14.069L17.462,14.276L17.361,14.462L17.228,14.624L17.067,14.757L16.881,14.859L16.674,14.924L16.452,14.947L16.231,14.924L16.024,14.859L15.838,14.757L15.677,14.623L15.544,14.462L15.443,14.276L15.379,14.069L15.357,13.849L15.357,13.174L13.619,13.174C12.954,13.174 12.328,12.996 11.786,12.685L11.786,30.661C12.328,30.35 12.954,30.172 13.619,30.172L27.098,30.172C27.763,30.172 28.389,30.35 28.931,30.661ZM32.658,22.968L31.077,22.968L30.59,22.969C30.442,22.969 30.322,23.092 30.322,23.241L30.322,31.045C30.322,31.194 30.442,31.318 30.59,31.318L34.059,31.318C34.207,31.318 34.327,31.194 34.327,31.046L34.327,23.241C34.327,23.092 34.207,22.969 34.058,22.969L32.658,22.968ZM31.077,21.718L32.658,21.718C32.806,21.718 32.926,21.595 32.926,21.446L32.926,18.594C32.926,18.445 32.806,18.321 32.658,18.321L31.077,18.321C30.929,18.321 30.809,18.445 30.809,18.594L30.809,21.446C30.809,21.595 30.929,21.718 31.077,21.718ZM11.732,11.034L11.786,11.034L11.786,11.097C12.237,11.604 12.893,11.924 13.619,11.924L15.357,11.924L15.357,11.118L15.379,10.898L15.443,10.691L15.544,10.505L15.677,10.343L15.838,10.21L16.024,10.108L16.231,10.043L16.452,10.021L16.674,10.043L16.881,10.108L17.067,10.21L17.228,10.343L17.361,10.505L17.462,10.691L17.526,10.898L17.548,11.118L17.548,11.924L23.656,11.924L23.656,11.118L23.678,10.898L23.742,10.691L23.843,10.505L23.976,10.343L24.137,10.21L24.324,10.108L24.53,10.043L24.752,10.021L24.973,10.043L25.18,10.108L25.366,10.21L25.527,10.343L25.66,10.505L25.761,10.691L25.825,10.898L25.848,11.118L25.848,11.924L27.098,11.924C27.824,11.924 28.479,11.604 28.931,11.097L28.931,11.034L28.985,11.034C29.344,10.603 29.56,10.049 29.56,9.447C29.56,8.086 28.453,6.971 27.098,6.971L13.619,6.971C12.264,6.971 11.156,8.086 11.156,9.447C11.156,10.049 11.373,10.603 11.732,11.034ZM8.393,21.718L9.974,21.718C10.122,21.718 10.241,21.595 10.241,21.446L10.242,18.594C10.242,18.445 10.121,18.321 9.973,18.321L8.393,18.321C8.244,18.321 8.124,18.445 8.124,18.594L8.124,21.446C8.124,21.595 8.244,21.718 8.393,21.718ZM29.56,33.899C29.56,32.537 28.453,31.422 27.098,31.422L13.619,31.422C12.264,31.422 11.156,32.537 11.156,33.899C11.156,35.26 12.264,36.375 13.619,36.375L27.098,36.375C28.453,36.375 29.56,35.26 29.56,33.899ZM24.127,4.528C24.127,4.032 23.724,3.625 23.231,3.625L17.502,3.625C17.008,3.625 16.606,4.032 16.606,4.528C16.606,5.023 17.008,5.43 17.502,5.43L23.23,5.43C23.724,5.43 24.127,5.023 24.127,4.528ZM26.954,25.303C26.954,26.658 25.849,27.765 24.502,27.764L16.284,27.764C14.936,27.764 13.832,26.658 13.832,25.303L13.832,21.348C13.832,19.994 14.936,18.887 16.284,18.887L24.502,18.887C25.85,18.887 26.954,19.994 26.954,21.348L26.954,25.303ZM25.704,25.303L25.704,21.348C25.704,20.683 25.164,20.137 24.502,20.137L16.284,20.137C15.622,20.137 15.082,20.683 15.082,21.348L15.082,25.303C15.082,25.969 15.622,26.514 16.284,26.514L24.502,26.514C25.164,26.514 25.704,25.968 25.704,25.303Z"/>
</svg>
</span><span class="sppb-tab-title">Short Trips</span></div><div class="sppb-tab" role="tab" data-tab="57"><span class="sppb-tab-icon"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1475_17095)">
<path d="M16.8398 7.00778C16.8218 6.99636 14.9201 5.79033 13.4831 4.07037C11.2317 1.37279 9.77667 0.358626 6.09958 0.0319926C4.8408 -0.0799307 3.93845 0.10737 3.41959 0.587041C3.01128 0.966209 3 1.38649 3 1.43446C3 1.56694 3.07444 1.688 3.19401 1.7451L4.38737 2.31386C4.38737 2.31386 4.40316 2.32071 4.40993 2.32299C4.44151 2.33441 5.19046 2.64277 5.21302 3.58156L5.23783 4.67338C5.0709 4.78074 4.92878 4.92692 4.8205 5.10509C4.79117 5.13706 4.76861 5.17361 4.75056 5.21472L4.03319 7.07174C3.99936 7.15625 4.00161 7.25218 4.03771 7.33441C4.06026 7.38695 4.10087 7.43035 4.14599 7.46461C4.06929 7.69988 3.5166 9.50892 3.89333 11.6309C4.20464 13.3965 5.36416 17.7044 5.84692 19.4747C5.91009 19.7076 6.08379 19.8904 6.31163 19.9635C6.38157 19.9863 6.45375 19.9977 6.52594 19.9977C6.68611 19.9977 6.84402 19.9429 6.97035 19.8378L8.15018 18.8648C8.3893 18.6684 8.49984 18.344 8.42765 18.0379C8.42765 18.0311 8.42314 18.0219 8.42088 18.0151L6.55753 11.8867C6.55076 11.8662 5.94618 9.91779 6.53045 8.41025C6.56655 8.42395 6.6049 8.43081 6.64099 8.43081C6.77635 8.43081 6.90493 8.34858 6.95682 8.21381L7.61553 6.50755C7.6223 6.49156 7.63132 6.47786 7.63809 6.45959C7.64712 6.43446 7.65614 6.40705 7.66516 6.37964L7.67419 6.35451C7.68547 6.3271 7.68998 6.2997 7.69449 6.27228C7.73961 6.07128 7.74186 5.86571 7.69449 5.66699L8.37802 4.94063C8.40284 4.91322 9.01192 4.29421 9.77667 4.5843C9.99323 4.66653 10.3339 4.93606 10.7264 5.24899C11.8566 6.14666 13.729 7.63821 16.4857 7.63821C16.5466 7.63821 16.6052 7.63821 16.6661 7.63821C16.815 7.63364 16.9459 7.53314 16.9842 7.38467C17.0226 7.23848 16.9639 7.08316 16.8353 7.00321L16.8398 7.00778ZM11.1482 4.7145C10.6926 4.3536 10.3339 4.06808 10.018 3.94702C9.78795 3.86022 9.56913 3.82596 9.3661 3.82596C8.51563 3.82596 7.92459 4.4404 7.89301 4.47238L7.35385 5.04341C7.22301 4.90636 7.0651 4.79673 6.88463 4.72592L6.4312 4.54775C6.262 4.48151 6.08605 4.4541 5.91234 4.45867L5.89204 3.56557C5.86046 2.22934 4.81824 1.74967 4.6671 1.688L3.7467 1.24944C3.78053 1.19462 3.82791 1.13524 3.89784 1.07356C4.15952 0.845149 4.7438 0.598461 6.04093 0.712669C9.51047 1.02103 10.8121 1.93012 12.9665 4.50892C13.8079 5.51623 14.778 6.34081 15.4886 6.88215C13.5124 6.58978 12.1205 5.48425 11.1505 4.71221L11.1482 4.7145ZM7.72607 18.3349L6.54625 19.3079C6.54625 19.3079 6.53271 19.3171 6.51918 19.3125C6.50564 19.3079 6.50338 19.2988 6.50113 19.2942C6.40638 18.9447 6.25298 18.3851 6.07477 17.7159L7.37641 16.8981L7.77345 18.2047C7.78247 18.2526 7.76442 18.3029 7.72607 18.3349ZM5.02127 7.09229L4.79117 7.00321L4.93103 6.64232L5.08218 6.25401L5.21528 5.90682L5.26265 5.78348L5.30777 5.80175L5.77022 5.9822L6.88463 6.41619L6.92298 6.42989L6.73574 6.91185L6.5846 7.30244L6.44924 7.64963L6.20561 7.55369L5.01901 7.09001L5.02127 7.09229ZM5.67548 5.20787C5.70706 5.19417 5.73864 5.18046 5.77022 5.17133C5.83113 5.15305 5.89204 5.14392 5.9552 5.14392C6.0319 5.14392 6.11086 5.15762 6.18756 5.18731L6.64099 5.36548C6.7786 5.41801 6.88688 5.51623 6.96133 5.64414C6.97486 5.66699 6.9884 5.68983 6.99968 5.71267C7.00193 5.71724 7.00193 5.72409 7.00419 5.72866L5.67322 5.21016L5.67548 5.20787ZM4.77538 7.72957L5.90106 8.16813C5.23107 9.9018 5.88527 12.0009 5.9146 12.0946L7.16887 16.2198L5.88978 17.0215C5.38672 15.1188 4.76861 12.6907 4.55881 11.5076C4.23171 9.65739 4.68289 8.02651 4.77312 7.725L4.77538 7.72957Z" fill="currentColor"/>
<path d="M7.32469 1.55778C7.02466 1.58291 6.75395 1.72681 6.55995 1.95979C6.36594 2.19278 6.27345 2.48743 6.30052 2.79122C6.32534 3.09501 6.46746 3.36911 6.69756 3.56555C6.90284 3.73914 7.1555 3.83279 7.41944 3.83279C7.45328 3.83279 7.48486 3.83279 7.5187 3.82823C8.13681 3.77341 8.59701 3.22064 8.54287 2.59478C8.51805 2.29099 8.37593 2.0169 8.14583 1.82046C7.91573 1.62402 7.62247 1.53037 7.32469 1.55778ZM7.13294 3.04019C7.04045 2.96253 6.98405 2.85289 6.97503 2.73183C6.96601 2.61077 7.0021 2.49428 7.0788 2.40063C7.1555 2.30698 7.26378 2.24988 7.38335 2.24074C7.39688 2.24074 7.41042 2.24074 7.42169 2.24074C7.65179 2.24074 7.84806 2.41891 7.86836 2.65646C7.87964 2.77752 7.84129 2.89401 7.76459 2.98766C7.68789 3.08131 7.57961 3.13841 7.46004 3.14755C7.34048 3.15897 7.22543 3.12014 7.13294 3.04248V3.04019Z" fill="currentColor"/>
</g>
<defs>
<clipPath id="clip0_1475_17095">
<rect width="14" height="20" fill="currentColor" transform="translate(3)"/>
</clipPath>
</defs>
</svg>
</span><span class="sppb-tab-title">Epic Peaks</span></div><div class="sppb-tab" role="tab" data-tab="44"><span class="sppb-tab-icon"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1475_17103)">
<path d="M11.6097 14.2995C12.9636 12.0134 14.8529 8.49982 14.8529 6.69444C14.8529 3.83021 12.6759 1.5 10 1.5C7.32408 1.5 5.14706 3.83021 5.14706 6.69444C5.14706 8.49982 7.03638 12.0134 8.39034 14.2995C5.77667 14.4538 2.5 15.0012 2.5 16.375C2.5 18.0559 7.40593 18.5 10 18.5C12.5941 18.5 17.5 18.0559 17.5 16.375C17.5 15.0012 14.2233 14.4538 11.6097 14.2995ZM3.16176 16.375C3.16176 15.9397 5.14119 15.1311 8.80044 14.9823C9.30829 15.8164 9.67787 16.3823 9.72922 16.4605C9.79126 16.5548 9.89229 16.6111 10 16.6111C10.1077 16.6111 10.2087 16.5548 10.2708 16.4605C10.3221 16.3823 10.6917 15.8164 11.1996 14.9823C14.8588 15.1311 16.8382 15.9397 16.8382 16.375C16.8382 16.8558 14.4245 17.7917 10 17.7917C5.57553 17.7917 3.16176 16.8558 3.16176 16.375ZM10 2.20833C12.311 2.20833 14.1912 4.22081 14.1912 6.69444C14.1912 8.40929 12.1644 12.0636 10.8427 14.2644C10.7706 14.3845 10.7004 14.5005 10.633 14.6114C10.5581 14.7344 10.4867 14.851 10.4187 14.9612C10.2549 15.2268 10.1119 15.4549 10 15.6314C9.88814 15.4549 9.74505 15.2268 9.58128 14.9612C9.51332 14.851 9.44191 14.7344 9.36705 14.6114C9.29957 14.5005 9.22945 14.3845 9.15734 14.2644C7.83564 12.0636 5.80882 8.40929 5.80882 6.69444C5.80882 4.22081 7.68899 2.20833 10 2.20833Z" fill="currentColor"/>
<path d="M10.0003 9.99999C11.7639 9.99999 13.1988 8.46412 13.1988 6.57638C13.1988 4.68865 11.7639 3.15277 10.0003 3.15277C8.23666 3.15277 6.80176 4.68865 6.80176 6.57638C6.80176 8.46412 8.23666 9.99999 10.0003 9.99999ZM10.0003 3.8611C11.399 3.8611 12.5371 5.07924 12.5371 6.57638C12.5371 8.07352 11.399 9.29166 10.0003 9.29166C8.60158 9.29166 7.46352 8.07352 7.46352 6.57638C7.46352 5.07924 8.60158 3.8611 10.0003 3.8611Z" fill="currentColor"/>
</g>
<defs>
<clipPath id="clip0_1475_17103">
<rect width="15" height="17" fill="currentColor" transform="translate(2.5 1.5)"/>
</clipPath>
</defs>
</svg>
</span><span class="sppb-tab-title">Remote Adventures</span></div><div class="sppb-tab" role="tab" data-tab="42"><span class="sppb-tab-icon"><svg width="41" height="40" viewBox="0 0 41 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1060_2283)">
<path d="M28.5713 22.1507V6.08651C28.5713 4.66016 27.4066 3.5 25.9747 3.5C24.5428 3.5 23.3782 4.66016 23.3782 6.08651V9.8743C23.0514 9.6997 22.6785 9.6001 22.2824 9.6001C21.7081 9.6001 21.1827 9.80955 20.7764 10.1544C20.3701 9.80955 19.8447 9.6001 19.2704 9.6001C18.6961 9.6001 18.1707 9.80955 17.7644 10.1544V6.08651C17.7644 4.66016 16.5997 3.5 15.1678 3.5C13.736 3.5 12.5713 4.66016 12.5713 6.08651V17.9279C12.5713 18.0512 12.583 18.1715 12.5998 18.2902C12.5826 18.4515 12.5713 18.6039 12.5713 18.7289V22.1507C12.5713 24.398 14.0593 26.3966 16.1735 27.0795V28.9767H15.619C14.7327 28.9767 14.012 29.6177 14.012 30.4055V35.887C14.012 36.2254 14.2876 36.5 14.6274 36.5H27.5957C27.9355 36.5 28.2111 36.2254 28.2111 35.887V30.4055C28.2111 29.6177 27.4904 28.9767 26.6045 28.9767H26.0497V26.5874C27.6142 25.6519 28.5713 23.9849 28.5713 22.1507ZM26.9803 30.4055V35.274H15.2428V30.4055C15.2428 30.3341 15.3858 30.2027 15.619 30.2027H26.6045C26.8377 30.2027 26.9803 30.3341 26.9803 30.4055ZM24.8189 26.2262V28.9767H17.4042V26.6113C17.4042 26.3232 17.2031 26.0742 16.9206 26.0127C15.1138 25.6184 13.8021 23.9941 13.8021 22.1507V18.7289C13.8021 18.6125 13.8189 18.4373 13.8441 18.2474C13.872 18.037 13.9104 17.8102 13.9492 17.6211L13.9365 17.6127C13.9441 17.6014 13.951 17.5897 13.9579 17.5778C13.9941 17.5148 14.0211 17.4452 14.0328 17.3692C14.0463 17.282 14.0684 17.2048 14.0869 17.1233C14.3228 16.0862 14.9441 15.505 15.5704 15.1828C15.9145 15.0057 16.2552 14.904 16.5336 14.8477C16.6983 14.8145 16.8384 14.7956 16.9439 14.7858C17.0543 14.7756 17.1299 14.7732 17.1454 14.7731L17.1491 14.7731L17.5594 14.7755L17.7644 14.7767L18.1747 14.7791L18.4895 14.7809L19.9559 14.7894L20.3662 14.7918L20.4543 14.7923C20.4496 14.8242 20.4417 14.8516 20.4363 14.8825C20.417 14.9927 20.3939 15.0972 20.3662 15.1949C20.2692 15.5366 20.1246 15.804 19.9559 16.0138C19.3754 16.7358 18.5168 16.7757 18.3746 16.7765H17.149C17.0767 16.7765 17.0083 16.7913 16.9439 16.8141C16.7055 16.8986 16.5336 17.1232 16.5336 17.3895V18.489C16.5336 18.6523 16.5989 18.8091 16.7155 18.9241C16.7483 18.9565 16.7851 18.9837 16.8237 19.0077C16.9148 19.0644 17.0194 19.0962 17.1279 19.1C17.135 19.1002 17.1419 19.102 17.149 19.102H17.1534C17.264 19.1024 17.3637 19.1162 17.4674 19.1251C17.613 19.1375 17.7514 19.1584 17.8829 19.1878C18.3017 19.2814 18.6552 19.4524 18.9178 19.7246C18.9658 19.7743 19.0099 19.8262 19.0513 19.8795C19.2041 20.076 19.3144 20.2935 19.3938 20.5082C19.5057 20.8108 19.5562 21.1058 19.5789 21.3293C19.602 21.5568 19.5967 21.7102 19.5965 21.7145C19.5793 22.0517 19.8389 22.3395 20.1775 22.3578C20.1891 22.3582 20.2003 22.3586 20.2115 22.3586C20.536 22.3586 20.8077 22.1056 20.8257 21.7791C20.8281 21.7317 20.8621 20.967 20.5605 20.124C20.6359 20.0738 20.7075 20.0186 20.7764 19.9601C21.1827 20.305 21.7081 20.5144 22.2824 20.5144C23.5653 20.5144 24.6089 19.4748 24.6089 18.1969V6.08651C24.6089 5.33622 25.2215 4.72601 25.9747 4.72601C26.7279 4.72601 27.3405 5.33622 27.3405 6.08651V22.1507C27.3405 23.6485 26.5036 25.0006 25.1566 25.6791C24.9495 25.7836 24.8189 25.9951 24.8189 26.2262ZM13.8021 6.08651C13.8021 5.33622 14.4146 4.72601 15.1678 4.72601C15.921 4.72601 16.5336 5.33622 16.5336 6.08651V13.5976C15.6766 13.7262 14.5914 14.1297 13.8021 15.0169V6.08651ZM18.1747 11.9176C18.1747 11.3158 18.6662 10.8261 19.2704 10.8261C19.5906 10.8261 19.8764 10.9661 20.077 11.1849C19.9995 11.4155 19.9559 11.6614 19.9559 11.9176V13.5632L18.1747 13.553V11.9176ZM23.3782 11.9176V18.1969C23.3782 18.7987 22.8866 19.2884 22.2824 19.2884C21.9622 19.2884 21.6764 19.1485 21.4759 18.9296C21.298 18.7355 21.1867 18.4798 21.1867 18.1969V16.3997C21.3701 16.0911 21.5073 15.7453 21.5969 15.3631C21.6022 15.3408 21.6061 15.3173 21.611 15.2947C21.6555 15.0905 21.6896 14.8783 21.7077 14.6541C21.72 14.5014 21.7271 14.3447 21.7271 14.1829C21.7271 14.1001 21.7103 14.0213 21.6805 13.9492C21.6594 13.8985 21.6302 13.8526 21.5969 13.8098C21.499 13.6836 21.3538 13.5979 21.1867 13.5774V11.9176C21.1867 11.6347 21.298 11.3791 21.4759 11.1849C21.6764 10.9661 21.9622 10.8261 22.2824 10.8261C22.8866 10.8261 23.3782 11.3158 23.3782 11.9176ZM19.9529 19.044C19.908 18.9901 19.8633 18.936 19.8137 18.8841C19.3998 18.4515 18.8834 18.1566 18.2712 18.0026H18.3694C18.4041 17.9962 19.1646 17.9994 19.9559 17.5777V18.1969C19.9559 18.4531 19.9995 18.699 20.077 18.9296C20.0388 18.9712 19.997 19.0086 19.9529 19.044Z" fill="currentColor"/>
</g>
<defs>
<clipPath id="clip0_1060_2283">
<rect width="16" height="33" fill="white" transform="translate(12.5713 3.5)"/>
</clipPath>
</defs>
</svg>
</span><span class="sppb-tab-title">Hardcore Adventures</span></div><div class="sppb-tab" role="tab" data-tab="60"><span class="sppb-tab-icon"><svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_1475_17084)">
<path d="M18.3406 4.10164H17.931V4.00914C17.931 3.51611 17.5057 3.11493 16.983 3.11493H16.3528C15.8302 3.11493 15.4049 3.51611 15.4049 4.00914V5.82833H13.6769C13.3876 5.54694 13.0361 5.37007 12.7012 5.26854V4.54468C12.7012 4.06212 12.2852 3.6697 11.7736 3.6697H11.6255C11.5384 3.6697 11.4556 3.68476 11.3755 3.70607C11.2918 3.30421 10.9164 3 10.4661 3H10.3183C9.83033 3 9.43371 3.35821 9.39771 3.80994C9.25307 3.72195 9.08187 3.66948 8.89732 3.66948H8.74922C8.26123 3.66948 7.86455 4.02779 7.82866 4.47962C7.68402 4.39165 7.51282 4.33918 7.32827 4.33918H7.18017C6.66863 4.33918 6.25265 4.7316 6.25265 5.21417V5.82833H4.59506V4.00914C4.59506 3.51611 4.16979 3.11493 3.64715 3.11493H3.01695C2.49431 3.11493 2.06905 3.51611 2.06905 4.00914V4.10164H1.65941C1.02015 4.10164 0.5 4.59232 0.5 5.19537V8.84421C0.5 9.44726 1.02015 9.93794 1.65941 9.93794H2.06905V10.0304C2.06905 10.5235 2.49431 10.9247 3.01695 10.9247H3.64715C4.16979 10.9247 4.59506 10.5235 4.59506 10.0304V7.96452H6.12199V8.89313C6.12199 9.19455 6.20691 9.49169 6.36768 9.75209L7.10743 10.9515L7.51147 11.6065C7.62854 11.7964 7.69058 12.0133 7.69058 12.2335V16.6719C7.69058 16.853 7.84638 17 8.03841 17C8.23043 17 8.38623 16.853 8.38623 16.6719V12.2335C8.38623 11.8975 8.29157 11.5664 8.11268 11.2765L6.9689 9.42205C6.86994 9.26162 6.81763 9.07876 6.81763 8.89313V7.99279C6.92907 8.0376 7.05155 8.06258 7.18017 8.06258H7.32827C7.47925 8.06258 7.61988 8.02514 7.74595 7.96452H8.02389C8.19386 8.1677 8.45411 8.30076 8.74922 8.30076H8.89732C9.19243 8.30076 9.45268 8.1677 9.62265 7.96452H9.90059C10.0267 8.02514 10.1673 8.06258 10.3183 8.06258H10.337C9.9636 8.6096 9.99496 9.25269 9.99672 9.28384C10.0074 9.46413 10.1684 9.59829 10.3615 9.59295C10.3851 9.59181 10.408 9.58839 10.4301 9.58308C10.5868 9.54528 10.6992 9.40747 10.6915 9.24923C10.6912 9.24325 10.6663 8.64127 11.0427 8.27192C11.2512 8.06749 11.5637 7.97136 11.9559 7.96794C11.9699 7.96801 11.9828 7.96582 11.9961 7.96452C12.0773 7.95662 12.1468 7.92551 12.2034 7.87267C12.2693 7.81114 12.3062 7.72719 12.3062 7.63982V7.09082C12.3062 7.08396 12.3045 7.07752 12.3041 7.07077C12.2939 6.91412 12.1681 6.78816 12.0055 6.76717C11.99 6.76517 11.9746 6.7627 11.9584 6.7627H11.3121C11.2801 6.7625 11.1768 6.75868 11.0459 6.72497C10.9404 6.6978 10.8171 6.65054 10.698 6.57027C10.6611 6.54537 10.6249 6.51634 10.5896 6.48457C10.4978 6.40219 10.4141 6.2951 10.3491 6.15643C10.3048 6.06215 10.2689 5.95429 10.2461 5.82833C10.2454 5.82397 10.2441 5.82033 10.2433 5.81594L10.698 5.81344L11.0459 5.81153L11.3937 5.80962L11.9571 5.80654C11.9623 5.80658 11.9811 5.8072 12.0055 5.80853C12.0466 5.81073 12.11 5.81594 12.1918 5.82833C12.2404 5.8357 12.295 5.84631 12.3533 5.85962C12.4601 5.88396 12.5789 5.91883 12.7012 5.97129C12.8099 6.01794 12.9196 6.07961 13.0244 6.15643C13.1424 6.24304 13.2536 6.34948 13.3459 6.48457C13.4513 6.63876 13.5329 6.82773 13.5741 7.06006V8.89313C13.5741 9.07898 13.5218 9.26184 13.4229 9.42205L12.2793 11.2763C12.1002 11.5664 12.0055 11.8975 12.0055 12.2335V16.6719C12.0055 16.853 12.1613 17 12.3533 17C12.5454 17 12.7012 16.853 12.7012 16.6719V12.2335C12.7012 12.0133 12.7632 11.7964 12.8805 11.6065L14.0241 9.75231C14.1848 9.49191 14.2698 9.19476 14.2698 8.89313V7.96452H15.4049V10.0304C15.4049 10.5235 15.8302 10.9247 16.3528 10.9247H16.983C17.5057 10.9247 17.931 10.5235 17.931 10.0304V9.93794H18.3406C18.9799 9.93794 19.5 9.44726 19.5 8.84421V5.19537C19.5 4.59232 18.9799 4.10164 18.3406 4.10164ZM18.8044 8.84421C18.8044 9.08539 18.5963 9.2817 18.3406 9.2817H17.931V4.75788H18.3406C18.5963 4.75788 18.8044 4.95419 18.8044 5.19537V8.84421ZM14.2657 6.98401C14.2357 6.80091 14.1875 6.63503 14.1249 6.48457H15.4049V7.30828H14.2698V7.034C14.2698 7.01733 14.2684 7.00046 14.2657 6.98401ZM16.1006 10.0304V4.00914C16.1006 3.87798 16.2138 3.77117 16.3528 3.77117H16.983C17.1221 3.77117 17.2353 3.87798 17.2353 4.00914V10.0304C17.2353 10.1616 17.1221 10.2684 16.983 10.2684H16.3528C16.2138 10.2684 16.1006 10.1616 16.1006 10.0304ZM11.3937 5.15334V4.54468C11.3937 4.42399 11.4976 4.32594 11.6255 4.32594H11.7736C11.9016 4.32594 12.0055 4.42399 12.0055 4.54468V5.15195C11.9894 5.15157 11.9719 5.1503 11.9564 5.1503L11.3937 5.15334ZM8.89732 7.64452H8.74922C8.73431 7.64452 8.72131 7.63905 8.70716 7.6365C8.60005 7.61722 8.51734 7.5324 8.51734 7.42577V4.54447C8.51734 4.42378 8.62128 4.32572 8.74922 4.32572H8.89732C9.02526 4.32572 9.1292 4.42378 9.1292 4.54447V7.42577C9.1292 7.5324 9.04649 7.61722 8.93938 7.6365C8.92523 7.63905 8.91224 7.64452 8.89732 7.64452ZM4.85661 6.48457H6.25265V7.18759C6.25265 7.22892 6.25967 7.26847 6.26558 7.30828H4.85661V6.48457ZM6.94829 5.82833V5.21417C6.94829 5.09347 7.05223 4.99542 7.18017 4.99542H7.32827C7.45621 4.99542 7.56015 5.09347 7.56015 5.21417V7.18759C7.56015 7.23321 7.54184 7.27321 7.51645 7.30828C7.47467 7.36601 7.40787 7.40634 7.32827 7.40634H7.18017C7.10058 7.40634 7.03378 7.36601 6.992 7.30828C6.96661 7.27321 6.94829 7.23321 6.94829 7.18759V5.82833ZM10.6669 7.29067C10.6633 7.29694 10.6586 7.30241 10.6543 7.30828C10.6125 7.36601 10.5457 7.40634 10.4661 7.40634H10.3183C10.2387 7.40634 10.1719 7.36601 10.1301 7.30828C10.1047 7.27321 10.0864 7.23321 10.0864 7.18759V6.94182C10.272 7.1121 10.4772 7.22035 10.6669 7.29067ZM10.0864 3.87499C10.0864 3.75429 10.1903 3.65624 10.3183 3.65624H10.4661C10.5941 3.65624 10.698 3.75429 10.698 3.87499V5.15712L10.0864 5.16043V3.87499ZM3.89942 10.0304C3.89942 10.1616 3.78619 10.2684 3.64715 10.2684H3.01695C2.87792 10.2684 2.76469 10.1616 2.76469 10.0304V4.00914C2.76469 3.87798 2.87792 3.77117 3.01695 3.77117H3.64715C3.78619 3.77117 3.89942 3.87798 3.89942 4.00914V10.0304ZM1.19564 8.84421V5.19537C1.19564 4.95419 1.40375 4.75788 1.65941 4.75788H2.06905V9.2817H1.65941C1.40375 9.2817 1.19564 9.08539 1.19564 8.84421Z" fill="currentColor"/>
</g>
<defs>
<clipPath id="clip0_1475_17084">
<rect width="19" height="14" fill="currentColor" transform="translate(0.5 3)"/>
</clipPath>
</defs>
</svg>
</span><span class="sppb-tab-title">Training Weekends</span></div><div class="sppb-tab" role="tab"><a href="/holidays#/holidays?sort=ordering%7CASC"><span class="sppb-tab-icon"><svg width="40" height="40" viewBox="0 0 40 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.9257 10.4508C22.3624 8.66689 20.2017 7.60038 17.8386 7.44867C15.4783 7.28957 13.1971 8.07475 11.4212 9.64356C7.75013 12.8785 7.38921 18.5102 10.6168 22.1942C12.3636 24.1932 14.8179 25.2111 17.2792 25.2111C19.3577 25.2111 21.442 24.4834 23.1228 23.0014C24.9004 21.4326 25.9631 19.2642 26.1143 16.8926C26.2669 14.5195 25.4889 12.2345 23.9257 10.4508ZM24.6126 16.7938C24.4849 18.7619 23.6013 20.5649 22.129 21.8656C19.0817 24.5539 14.4212 24.252 11.7424 21.1939C9.06356 18.1357 9.36447 13.4586 12.4117 10.7701C13.7651 9.57399 15.4707 8.93026 17.2584 8.93026C17.4198 8.93026 17.5813 8.93468 17.7428 8.94499C19.7038 9.07315 21.5005 9.95995 22.7965 11.4375C24.0941 12.9282 24.7399 14.8256 24.6122 16.7936L24.6126 16.7938Z" fill="white"/>
<path d="M27.6501 22.9072C28.7392 21.1837 29.3865 19.2083 29.5216 17.1152C29.7315 13.8302 28.657 10.6632 26.4934 8.18824C24.3284 5.71492 21.3326 4.23612 18.0593 4.02532C14.7861 3.81761 11.6303 4.89297 9.16416 7.0643C4.08115 11.5512 3.58213 19.3513 8.05009 24.4525C10.4735 27.2204 13.867 28.6346 17.2786 28.6346C19.0414 28.6346 20.8072 28.2531 22.4454 27.4916L29.3076 35.3283C29.6541 35.7246 30.134 35.9617 30.661 35.9956C30.7065 36 30.7476 36 30.7887 36C31.2657 36 31.7208 35.8262 32.0848 35.5095L34.3321 33.5252C35.1482 32.8049 35.226 31.5571 34.5126 30.7381L27.6501 22.9072ZM9.18036 23.4567C5.25697 18.98 5.69718 12.1317 10.1609 8.19842C12.2056 6.39538 14.7419 5.51298 17.2667 5.51298C20.2581 5.51298 23.238 6.75334 25.3648 9.18246C29.2882 13.6591 28.848 20.5075 24.3843 24.4407C19.9205 28.3781 13.0996 27.9378 9.18036 23.4567ZM23.7985 26.7535C24.3474 26.4058 24.8729 26.0184 25.3764 25.5735C25.8755 25.1316 26.3305 24.6602 26.7444 24.155L30.9763 28.9854L28.0304 31.5839L23.7985 26.7535ZM33.3381 32.3909L31.0908 34.3751C30.9969 34.4576 30.8765 34.4989 30.7561 34.4886C30.6328 34.4812 30.5198 34.4237 30.4362 34.3295L29.0197 32.712L31.9656 30.1135L33.3821 31.731C33.5494 31.9269 33.5304 32.2215 33.3381 32.3909Z" fill="white"/>
</svg>
</span><span class="sppb-tab-title">Search All Adventures</span></a></div></div></div><div class="sppb-tab-content"><div id="sppb-content-holiday-tab-55" class="sppb-tab-pane sppb-fade active in" role="tabpanel" data-category-id="55" data-loaded="true"><div class="category-header"><div class="category-header-content flex-grow-1 pe-4"><h2 class="category-title">Best Sellers</h2></div><div class="category-header-action flex-shrink-0"><a href="https://evertrek.co.uk/holidays#/holidays?FC.Collections=Best+Sellers&sort=ordering%7CASC&page=1" class="btn btn-primary col-12 col-md-auto">View more Best Sellers <svg width="24" height="25" viewBox="0 0 24 25" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.00002 7.21051C8.61002 7.60051 8.61002 8.23051 9.00002 8.62051L12.88 12.5005L9.00002 16.3805C8.61002 16.7705 8.61002 17.4005 9.00002 17.7905C9.39002 18.1805 10.02 18.1805 10.41 17.7905L15 13.2005C15.39 12.8105 15.39 12.1805 15 11.7905L10.41 7.20051C10.03 6.82051 9.39002 6.82051 9.00002 7.21051Z" fill="black"/>
</svg>
</a></div></div><div class="holiday-grid-container"><div class="holiday-grid">
<style>
.difficulty-info-btn {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0;
}

.difficulty-info-btn:hover {
    opacity: 0.8;
}

.difficulty-icon {
    display: flex;
    align-items: center;
}

.difficulty-text {
    margin: 0 4px;
}
</style>

<div class="sppb-card-item">

	<div class="zen-card zen-card--related ">
		<a href="/holidays/everest-base-camp-trek" class="zen-link zen-link--dark zen-link--image-overlay-expand d-inline-block">
			<div class="zen-card__body">
				<div class="zen-card__image zen-card__image--max-height mb-3">
					<img class="img-fluid" src="https://i.assetzen.net/i/yV7LWkQcW3et/w:515/h:275/q:70.webp" alt="Everest Base Camp Trek">
					<div class="zen-card__image-overlay">
						<ul class="zen-list zen-list--inline">
							<li class="zen-text zen-text--light overlay-message text-center">
								Available dates in:							</li>
																													<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Apr									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Dec									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Feb									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Jun									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Mar									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										May									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Nov									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Oct									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Sep									</span>
								</li>
																					</ul>
					</div>
				</div>

				<div class="zen-card__info-wrap">
										<h5 class="zen-title zen-title--font-semibold zen-capitalize">
						Everest Base Camp Trek					</h5>
											<p class="zen-text zen-text--subtitle mb-1">
							<span>Nepal, Asia</span><span>15 nights</span>						</p>
										<div class="zen-card__info">
						<div class="row">
							<div class="col">
								<div class="d-flex justify-content-start align-items-center h-100">
									<img src="/templates/zenbase/icons/altitude.svg" alt="">
									<span>4000 - 6000m</span>
								</div>
							</div>
							<div class="col text-right">
								<div class="d-flex justify-content-end align-items-center h-100">
									<div class="difficulty-info-section" onclick="event.preventDefault(); event.stopPropagation();">
										<button class="difficulty-info-btn d-flex align-items-center" 
											data-id="1" 
											type="button"
											data-bs-toggle="modal"
											data-bs-target="#difficultyModal-challenging">
											<span class="difficulty-icon">
												<svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 0 24 24" width="20px" fill="currentColor"><g fill="none"><path d="M0 0h24v24H0V0z"/><path d="M0 0h24v24H0V0z" opacity=".87"/></g><path d="M11 7h2v2h-2zm1 10c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1zm0-15C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>											</span>
											<span class="difficulty-text">Challenging</span>
																						<span class="difficulty-indicator ms-2" style="width: 14px; height: 14px; border-radius: 50%; background-color: #FE7720"></span>
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="zen-text zen-text--text-df mb-3">
						<ul>
<li><span style="font-weight: 400;">Fulfill your ultimate bucket-list dream: stand at the foot of Everest, follow in the footsteps of Tenzing and Hillary, and marvel at the world’s most iconic peak.</span></li>
<li><span style="font-weight: 400;">Immerse yourself in Sherpa culture, exploring ancient villages and monasteries in the heart of the Himalayas.</span></li>
<li><span style="font-weight: 400;">Push your limits on an epic adventure through glacial valleys and rugged trails, surrounded by the tallest mountains on Earth.</span></li>
</ul>					</div>
					<div class="zen-flex-end zen-price">
						<div class="row w-100 m-0">
							<div class="col-12 d-flex justify-content-between align-items-center p-0">
								<span class="">
									 									<span>£2,450pp</span>
								</span>
								<span class="zen-text zen-text--default-light zen-uppercase mx-2">
									or								</span>
								<span class="">
									<!-- Monthly Payment Pill  -->
																			<span class="p-0">
											£131 / 18											Months										</span>
																</div>
						</div>
						</span>
					</div>
				</div>


			</div>
		</a>
	</div>
</div>


<style>
.difficulty-modal .modal-dialog {
    max-width: 400px;
}

.difficulty-modal .modal-content {
    border-radius: 24px;
    border: none;
    padding: 24px;
    background-color: #FFFFFF;
}

.difficulty-modal .modal-header {
    border: none;
    padding: 0;
    position: relative;
}

.difficulty-modal .btn-close {
    position: absolute;
    right: 15px;
    top: 15px;
    background: none;
    opacity: 1;
    width: 24px;
    height: 24px;
    padding: 0;
    z-index: 2;
}

.difficulty-modal .btn-close::before {
    content: "×";
    font-size: 32px;
    line-height: 24px;
    color: #000;
}

.difficulty-modal .modal-body {
    text-align: center;
    padding: 0;
}

.difficulty-modal .grade-image {
    width: 120px;
    height: 120px;
    margin: 24px auto;
}

.difficulty-modal .difficulty-title {
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 24px;
    color: #000000;
}

.difficulty-modal .difficulty-description {
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    margin-bottom: 32px;
}

.difficulty-modal .more-info-btn {
    background: #FE7720;
    color: white;
    border: 1px solid #FE7720;
    border-radius: 100px;
    padding: 11px 23px;
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s, border-color 0.2s;
    height: 48px;
    box-sizing: border-box;
}

.difficulty-modal .more-info-btn:hover {
    background: #e66b1c;
    border-color: #e66b1c;
}

.difficulty-modal .more-info-btn::after {
    content: "›";
    font-size: 20px;
    line-height: 1;
}
</style>

<div class="modal fade difficulty-modal" id="difficultyModal-gentle" tabindex="-1" aria-labelledby="difficultyModalLabel-gentle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-1.svg" alt="Gentle difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Gentle</h2>
                <div class="difficulty-description">
                    Perfect for beginners or those looking for a relaxed adventure. These trips involve very light activities with minimal distances and elevation gain.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-easy" tabindex="-1" aria-labelledby="difficultyModalLabel-easy" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-1.svg" alt="Easy difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Easy</h2>
                <div class="difficulty-description">
                    Perfect for beginners or those looking for a gentle adventure. These trips involve light activities with moderate distances and minimal elevation gain.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-moderate" tabindex="-1" aria-labelledby="difficultyModalLabel-moderate" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-2.svg" alt="Moderate difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Moderate</h2>
                <div class="difficulty-description">
                    Suitable for those with some hiking experience. These trips include longer distances and moderate elevation gain, requiring a good level of fitness.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-challenging" tabindex="-1" aria-labelledby="difficultyModalLabel-challenging" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-3.svg" alt="Challenging difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Challenging</h2>
                <div class="difficulty-description">
                    For experienced hikers seeking a more demanding adventure. These trips involve significant distances and elevation gain, requiring excellent fitness and endurance.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-hardcore" tabindex="-1" aria-labelledby="difficultyModalLabel-hardcore" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-4.svg" alt="Hardcore difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Hardcore</h2>
                <div class="difficulty-description">
                    Our most demanding trips for seasoned adventurers. These expeditions involve challenging terrain, significant elevation gain, and require exceptional fitness and experience.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
 

<style>
.difficulty-info-btn {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0;
}

.difficulty-info-btn:hover {
    opacity: 0.8;
}

.difficulty-icon {
    display: flex;
    align-items: center;
}

.difficulty-text {
    margin: 0 4px;
}
</style>

<div class="sppb-card-item">

	<div class="zen-card zen-card--related ">
		<a href="/holidays/kilimanjaro-the-long-way" class="zen-link zen-link--dark zen-link--image-overlay-expand d-inline-block">
			<div class="zen-card__body">
				<div class="zen-card__image zen-card__image--max-height mb-3">
					<img class="img-fluid" src="https://i.assetzen.net/i/wM19kEWPBF5q/w:515/h:275/q:70.webp" alt="Kilimanjaro The Long Way">
					<div class="zen-card__image-overlay">
						<ul class="zen-list zen-list--inline">
							<li class="zen-text zen-text--light overlay-message text-center">
								Available dates in:							</li>
																													<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Aug									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Feb									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Jan									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Jul									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Jun									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Mar									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Nov									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Oct									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Sep									</span>
								</li>
																					</ul>
					</div>
				</div>

				<div class="zen-card__info-wrap">
										<h5 class="zen-title zen-title--font-semibold zen-capitalize">
						Kilimanjaro The Long Way					</h5>
											<p class="zen-text zen-text--subtitle mb-1">
							<span>Tanzania, Africa</span><span>11 nights</span>						</p>
										<div class="zen-card__info">
						<div class="row">
							<div class="col">
								<div class="d-flex justify-content-start align-items-center h-100">
									<img src="/templates/zenbase/icons/altitude.svg" alt="">
									<span>4000 - 6000m</span>
								</div>
							</div>
							<div class="col text-right">
								<div class="d-flex justify-content-end align-items-center h-100">
									<div class="difficulty-info-section" onclick="event.preventDefault(); event.stopPropagation();">
										<button class="difficulty-info-btn d-flex align-items-center" 
											data-id="3" 
											type="button"
											data-bs-toggle="modal"
											data-bs-target="#difficultyModal-challenging">
											<span class="difficulty-icon">
												<svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 0 24 24" width="20px" fill="currentColor"><g fill="none"><path d="M0 0h24v24H0V0z"/><path d="M0 0h24v24H0V0z" opacity=".87"/></g><path d="M11 7h2v2h-2zm1 10c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1zm0-15C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>											</span>
											<span class="difficulty-text">Challenging</span>
																						<span class="difficulty-indicator ms-2" style="width: 14px; height: 14px; border-radius: 50%; background-color: #FE7720"></span>
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="zen-text zen-text--text-df mb-3">
						<ul>
<li><span style="font-weight: 400;">Ascend under starlight and summit at sunrise to conquer Uhuru Peak and stand on the roof of Africa. </span></li>
<li><span style="font-weight: 400;">Take the path less traveled via the Lemosho route, known for its stunning beauty, lower crowds, and excellent acclimatization.</span></li>
<li><span style="font-weight: 400;">Trek through five distinct climatic zones, from lush rainforests to alpine deserts, before standing atop Africa’s highest peak at 5,895m</span></li>
</ul>					</div>
					<div class="zen-flex-end zen-price">
						<div class="row w-100 m-0">
							<div class="col-12 d-flex justify-content-between align-items-center p-0">
								<span class="">
									 									<span>£2,845pp</span>
								</span>
								<span class="zen-text zen-text--default-light zen-uppercase mx-2">
									or								</span>
								<span class="">
									<!-- Monthly Payment Pill  -->
																			<span class="p-0">
											£159 / 18											Months										</span>
																</div>
						</div>
						</span>
					</div>
				</div>


			</div>
		</a>
	</div>
</div>


<style>
.difficulty-modal .modal-dialog {
    max-width: 400px;
}

.difficulty-modal .modal-content {
    border-radius: 24px;
    border: none;
    padding: 24px;
    background-color: #FFFFFF;
}

.difficulty-modal .modal-header {
    border: none;
    padding: 0;
    position: relative;
}

.difficulty-modal .btn-close {
    position: absolute;
    right: 15px;
    top: 15px;
    background: none;
    opacity: 1;
    width: 24px;
    height: 24px;
    padding: 0;
    z-index: 2;
}

.difficulty-modal .btn-close::before {
    content: "×";
    font-size: 32px;
    line-height: 24px;
    color: #000;
}

.difficulty-modal .modal-body {
    text-align: center;
    padding: 0;
}

.difficulty-modal .grade-image {
    width: 120px;
    height: 120px;
    margin: 24px auto;
}

.difficulty-modal .difficulty-title {
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 24px;
    color: #000000;
}

.difficulty-modal .difficulty-description {
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    margin-bottom: 32px;
}

.difficulty-modal .more-info-btn {
    background: #FE7720;
    color: white;
    border: 1px solid #FE7720;
    border-radius: 100px;
    padding: 11px 23px;
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s, border-color 0.2s;
    height: 48px;
    box-sizing: border-box;
}

.difficulty-modal .more-info-btn:hover {
    background: #e66b1c;
    border-color: #e66b1c;
}

.difficulty-modal .more-info-btn::after {
    content: "›";
    font-size: 20px;
    line-height: 1;
}
</style>

<div class="modal fade difficulty-modal" id="difficultyModal-gentle" tabindex="-1" aria-labelledby="difficultyModalLabel-gentle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-1.svg" alt="Gentle difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Gentle</h2>
                <div class="difficulty-description">
                    Perfect for beginners or those looking for a relaxed adventure. These trips involve very light activities with minimal distances and elevation gain.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-easy" tabindex="-1" aria-labelledby="difficultyModalLabel-easy" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-1.svg" alt="Easy difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Easy</h2>
                <div class="difficulty-description">
                    Perfect for beginners or those looking for a gentle adventure. These trips involve light activities with moderate distances and minimal elevation gain.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-moderate" tabindex="-1" aria-labelledby="difficultyModalLabel-moderate" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-2.svg" alt="Moderate difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Moderate</h2>
                <div class="difficulty-description">
                    Suitable for those with some hiking experience. These trips include longer distances and moderate elevation gain, requiring a good level of fitness.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-challenging" tabindex="-1" aria-labelledby="difficultyModalLabel-challenging" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-3.svg" alt="Challenging difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Challenging</h2>
                <div class="difficulty-description">
                    For experienced hikers seeking a more demanding adventure. These trips involve significant distances and elevation gain, requiring excellent fitness and endurance.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-hardcore" tabindex="-1" aria-labelledby="difficultyModalLabel-hardcore" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-4.svg" alt="Hardcore difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Hardcore</h2>
                <div class="difficulty-description">
                    Our most demanding trips for seasoned adventurers. These expeditions involve challenging terrain, significant elevation gain, and require exceptional fitness and experience.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
 

<style>
.difficulty-info-btn {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0;
}

.difficulty-info-btn:hover {
    opacity: 0.8;
}

.difficulty-icon {
    display: flex;
    align-items: center;
}

.difficulty-text {
    margin: 0 4px;
}
</style>

<div class="sppb-card-item">

	<div class="zen-card zen-card--related ">
		<a href="/holidays/machu-picchu-via-tomacaya-route-the-hidden-valley" class="zen-link zen-link--dark zen-link--image-overlay-expand d-inline-block">
			<div class="zen-card__body">
				<div class="zen-card__image zen-card__image--max-height mb-3">
					<img class="img-fluid" src="https://i.assetzen.net/i/JNxh9D3HU5f2/w:515/h:275/q:70.webp" alt="Machu Picchu via Tomacaya route - The Hidden Valley">
					<div class="zen-card__image-overlay">
						<ul class="zen-list zen-list--inline">
							<li class="zen-text zen-text--light overlay-message text-center">
								Available dates in:							</li>
																													<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Apr									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Aug									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Jul									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Jun									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										May									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Sep									</span>
								</li>
																					</ul>
					</div>
				</div>

				<div class="zen-card__info-wrap">
										<h5 class="zen-title zen-title--font-semibold zen-capitalize">
						Machu Picchu via Tomacaya route - The Hidden Valley					</h5>
											<p class="zen-text zen-text--subtitle mb-1">
							<span>Peru, South America</span><span>11 nights</span>						</p>
										<div class="zen-card__info">
						<div class="row">
							<div class="col">
								<div class="d-flex justify-content-start align-items-center h-100">
									<img src="/templates/zenbase/icons/altitude.svg" alt="">
									<span>4000 - 6000m</span>
								</div>
							</div>
							<div class="col text-right">
								<div class="d-flex justify-content-end align-items-center h-100">
									<div class="difficulty-info-section" onclick="event.preventDefault(); event.stopPropagation();">
										<button class="difficulty-info-btn d-flex align-items-center" 
											data-id="7" 
											type="button"
											data-bs-toggle="modal"
											data-bs-target="#difficultyModal-moderate">
											<span class="difficulty-icon">
												<svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 0 24 24" width="20px" fill="currentColor"><g fill="none"><path d="M0 0h24v24H0V0z"/><path d="M0 0h24v24H0V0z" opacity=".87"/></g><path d="M11 7h2v2h-2zm1 10c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1zm0-15C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>											</span>
											<span class="difficulty-text">Moderate</span>
																						<span class="difficulty-indicator ms-2" style="width: 14px; height: 14px; border-radius: 50%; background-color: #FFBF06"></span>
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="zen-text zen-text--text-df mb-3">
						<ul>
<li>Arrive at Machu Picchu via the remote and hidden Tomacaya following an ancient trail shrouded in Inca legend and mystery.</li>
<li>Experience the wonder of Machu Picchu twice - view it from the majestic Sungate and again the next day to explore the ruins.</li>
<li>Immerse yourself in Andean culture, meeting local communities and exploring ancient Inca ruins along the way.</li>
</ul>					</div>
					<div class="zen-flex-end zen-price">
						<div class="row w-100 m-0">
							<div class="col-12 d-flex justify-content-between align-items-center p-0">
								<span class="">
									 									<span>£1,950pp</span>
								</span>
								<span class="zen-text zen-text--default-light zen-uppercase mx-2">
									or								</span>
								<span class="">
									<!-- Monthly Payment Pill  -->
																			<span class="p-0">
											£134 / 18											Months										</span>
																</div>
						</div>
						</span>
					</div>
				</div>


			</div>
		</a>
	</div>
</div>


<style>
.difficulty-modal .modal-dialog {
    max-width: 400px;
}

.difficulty-modal .modal-content {
    border-radius: 24px;
    border: none;
    padding: 24px;
    background-color: #FFFFFF;
}

.difficulty-modal .modal-header {
    border: none;
    padding: 0;
    position: relative;
}

.difficulty-modal .btn-close {
    position: absolute;
    right: 15px;
    top: 15px;
    background: none;
    opacity: 1;
    width: 24px;
    height: 24px;
    padding: 0;
    z-index: 2;
}

.difficulty-modal .btn-close::before {
    content: "×";
    font-size: 32px;
    line-height: 24px;
    color: #000;
}

.difficulty-modal .modal-body {
    text-align: center;
    padding: 0;
}

.difficulty-modal .grade-image {
    width: 120px;
    height: 120px;
    margin: 24px auto;
}

.difficulty-modal .difficulty-title {
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 24px;
    color: #000000;
}

.difficulty-modal .difficulty-description {
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    margin-bottom: 32px;
}

.difficulty-modal .more-info-btn {
    background: #FE7720;
    color: white;
    border: 1px solid #FE7720;
    border-radius: 100px;
    padding: 11px 23px;
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s, border-color 0.2s;
    height: 48px;
    box-sizing: border-box;
}

.difficulty-modal .more-info-btn:hover {
    background: #e66b1c;
    border-color: #e66b1c;
}

.difficulty-modal .more-info-btn::after {
    content: "›";
    font-size: 20px;
    line-height: 1;
}
</style>

<div class="modal fade difficulty-modal" id="difficultyModal-gentle" tabindex="-1" aria-labelledby="difficultyModalLabel-gentle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-1.svg" alt="Gentle difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Gentle</h2>
                <div class="difficulty-description">
                    Perfect for beginners or those looking for a relaxed adventure. These trips involve very light activities with minimal distances and elevation gain.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-easy" tabindex="-1" aria-labelledby="difficultyModalLabel-easy" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-1.svg" alt="Easy difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Easy</h2>
                <div class="difficulty-description">
                    Perfect for beginners or those looking for a gentle adventure. These trips involve light activities with moderate distances and minimal elevation gain.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-moderate" tabindex="-1" aria-labelledby="difficultyModalLabel-moderate" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-2.svg" alt="Moderate difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Moderate</h2>
                <div class="difficulty-description">
                    Suitable for those with some hiking experience. These trips include longer distances and moderate elevation gain, requiring a good level of fitness.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-challenging" tabindex="-1" aria-labelledby="difficultyModalLabel-challenging" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-3.svg" alt="Challenging difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Challenging</h2>
                <div class="difficulty-description">
                    For experienced hikers seeking a more demanding adventure. These trips involve significant distances and elevation gain, requiring excellent fitness and endurance.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-hardcore" tabindex="-1" aria-labelledby="difficultyModalLabel-hardcore" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-4.svg" alt="Hardcore difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Hardcore</h2>
                <div class="difficulty-description">
                    Our most demanding trips for seasoned adventurers. These expeditions involve challenging terrain, significant elevation gain, and require exceptional fitness and experience.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
 

<style>
.difficulty-info-btn {
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 0;
}

.difficulty-info-btn:hover {
    opacity: 0.8;
}

.difficulty-icon {
    display: flex;
    align-items: center;
}

.difficulty-text {
    margin: 0 4px;
}
</style>

<div class="sppb-card-item">

	<div class="zen-card zen-card--related ">
		<a href="/holidays/mt-toubkal-roof-of-the-north-weekender" class="zen-link zen-link--dark zen-link--image-overlay-expand d-inline-block">
			<div class="zen-card__body">
				<div class="zen-card__image zen-card__image--max-height mb-3">
					<img class="img-fluid" src="https://i.assetzen.net/i/W5UsXJMJhCZg/w:515/h:275/q:70.webp" alt="Mt Toubkal Roof of the North Weekender">
					<div class="zen-card__image-overlay">
						<ul class="zen-list zen-list--inline">
							<li class="zen-text zen-text--light overlay-message text-center">
								Available dates in:							</li>
																													<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Aug									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Feb									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Jan									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Jul									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Jun									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Mar									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										May									</span>
								</li>
																							<li class="list-inline-item">
									<span class="zen-text zen-text--light">
										Nov									</span>
								</li>
																					</ul>
					</div>
				</div>

				<div class="zen-card__info-wrap">
										<h5 class="zen-title zen-title--font-semibold zen-capitalize">
						Mt Toubkal Roof of the North Weekender					</h5>
											<p class="zen-text zen-text--subtitle mb-1">
							<span>Morocco, Africa</span><span>4 nights</span>						</p>
										<div class="zen-card__info">
						<div class="row">
							<div class="col">
								<div class="d-flex justify-content-start align-items-center h-100">
									<img src="/templates/zenbase/icons/altitude.svg" alt="">
									<span>4000 - 6000m</span>
								</div>
							</div>
							<div class="col text-right">
								<div class="d-flex justify-content-end align-items-center h-100">
									<div class="difficulty-info-section" onclick="event.preventDefault(); event.stopPropagation();">
										<button class="difficulty-info-btn d-flex align-items-center" 
											data-id="5" 
											type="button"
											data-bs-toggle="modal"
											data-bs-target="#difficultyModal-challenging">
											<span class="difficulty-icon">
												<svg xmlns="http://www.w3.org/2000/svg" height="20px" viewBox="0 0 24 24" width="20px" fill="currentColor"><g fill="none"><path d="M0 0h24v24H0V0z"/><path d="M0 0h24v24H0V0z" opacity=".87"/></g><path d="M11 7h2v2h-2zm1 10c.55 0 1-.45 1-1v-4c0-.55-.45-1-1-1s-1 .45-1 1v4c0 .55.45 1 1 1zm0-15C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>											</span>
											<span class="difficulty-text">Challenging</span>
																						<span class="difficulty-indicator ms-2" style="width: 14px; height: 14px; border-radius: 50%; background-color: #FE7720"></span>
										</button>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="zen-text zen-text--text-df mb-3">
						<ul>
<li><span style="font-weight: 400;">Climb Mount Toubkal (4,167m), the highest peak in North Africa, in just a few exhilarating days.</span></li>
<li><span style="font-weight: 400;">Trek through dramatic valleys and mountain passes in the heart of the High Atlas.</span></li>
<li><span style="font-weight: 400;">Sleep under the stars in a high-altitude refuge or tent, ready for an early summit start.</span></li>
</ul>					</div>
					<div class="zen-flex-end zen-price">
						<div class="row w-100 m-0">
							<div class="col-12 d-flex justify-content-between align-items-center p-0">
								<span class="">
									 									<span>£675pp</span>
								</span>
								<span class="zen-text zen-text--default-light zen-uppercase mx-2">
									or								</span>
								<span class="">
									<!-- Monthly Payment Pill  -->
																			<span class="p-0">
											£28 / 18											Months										</span>
																</div>
						</div>
						</span>
					</div>
				</div>


			</div>
		</a>
	</div>
</div>


<style>
.difficulty-modal .modal-dialog {
    max-width: 400px;
}

.difficulty-modal .modal-content {
    border-radius: 24px;
    border: none;
    padding: 24px;
    background-color: #FFFFFF;
}

.difficulty-modal .modal-header {
    border: none;
    padding: 0;
    position: relative;
}

.difficulty-modal .btn-close {
    position: absolute;
    right: 15px;
    top: 15px;
    background: none;
    opacity: 1;
    width: 24px;
    height: 24px;
    padding: 0;
    z-index: 2;
}

.difficulty-modal .btn-close::before {
    content: "×";
    font-size: 32px;
    line-height: 24px;
    color: #000;
}

.difficulty-modal .modal-body {
    text-align: center;
    padding: 0;
}

.difficulty-modal .grade-image {
    width: 120px;
    height: 120px;
    margin: 24px auto;
}

.difficulty-modal .difficulty-title {
    font-size: 32px;
    font-weight: 500;
    margin-bottom: 24px;
    color: #000000;
}

.difficulty-modal .difficulty-description {
    font-size: 16px;
    line-height: 1.5;
    color: #333;
    margin-bottom: 32px;
}

.difficulty-modal .more-info-btn {
    background: #FE7720;
    color: white;
    border: 1px solid #FE7720;
    border-radius: 100px;
    padding: 11px 23px;
    font-size: 16px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: background-color 0.2s, border-color 0.2s;
    height: 48px;
    box-sizing: border-box;
}

.difficulty-modal .more-info-btn:hover {
    background: #e66b1c;
    border-color: #e66b1c;
}

.difficulty-modal .more-info-btn::after {
    content: "›";
    font-size: 20px;
    line-height: 1;
}
</style>

<div class="modal fade difficulty-modal" id="difficultyModal-gentle" tabindex="-1" aria-labelledby="difficultyModalLabel-gentle" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-1.svg" alt="Gentle difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Gentle</h2>
                <div class="difficulty-description">
                    Perfect for beginners or those looking for a relaxed adventure. These trips involve very light activities with minimal distances and elevation gain.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-easy" tabindex="-1" aria-labelledby="difficultyModalLabel-easy" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-1.svg" alt="Easy difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Easy</h2>
                <div class="difficulty-description">
                    Perfect for beginners or those looking for a gentle adventure. These trips involve light activities with moderate distances and minimal elevation gain.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-moderate" tabindex="-1" aria-labelledby="difficultyModalLabel-moderate" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-2.svg" alt="Moderate difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Moderate</h2>
                <div class="difficulty-description">
                    Suitable for those with some hiking experience. These trips include longer distances and moderate elevation gain, requiring a good level of fitness.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-challenging" tabindex="-1" aria-labelledby="difficultyModalLabel-challenging" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-3.svg" alt="Challenging difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Challenging</h2>
                <div class="difficulty-description">
                    For experienced hikers seeking a more demanding adventure. These trips involve significant distances and elevation gain, requiring excellent fitness and endurance.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
<div class="modal fade difficulty-modal" id="difficultyModal-hardcore" tabindex="-1" aria-labelledby="difficultyModalLabel-hardcore" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <img src="/templates/zenbase/images/grading/grade-4.svg" alt="Hardcore difficulty grade" class="grade-image">
                <h2 class="difficulty-title">Hardcore</h2>
                <div class="difficulty-description">
                    Our most demanding trips for seasoned adventurers. These expeditions involve challenging terrain, significant elevation gain, and require exceptional fitness and experience.                </div>
                <a href="/knowledge-centre/evertrek-difficulty-ratings" class="more-info-btn">
                    More about difficulty ratings
                </a>
            </div>
        </div>
    </div>
</div>
 
</div></div></div><div id="sppb-content-holiday-tab-41" class="sppb-tab-pane sppb-fade" role="tabpanel" data-category-id="41"></div><div id="sppb-content-holiday-tab-56" class="sppb-tab-pane sppb-fade" role="tabpanel" data-category-id="56"></div><div id="sppb-content-holiday-tab-57" class="sppb-tab-pane sppb-fade" role="tabpanel" data-category-id="57"></div><div id="sppb-content-holiday-tab-44" class="sppb-tab-pane sppb-fade" role="tabpanel" data-category-id="44"></div><div id="sppb-content-holiday-tab-42" class="sppb-tab-pane sppb-fade" role="tabpanel" data-category-id="42"></div><div id="sppb-content-holiday-tab-60" class="sppb-tab-pane sppb-fade" role="tabpanel" data-category-id="60"></div><div id="sppb-content-holiday-tab-" class="sppb-tab-pane sppb-fade" role="tabpanel" data-category-id=""></div></div></div><script>
                jQuery(document).ready(function($) {
                    // Tab click handler
                    $(".sppb-tab").on("click", function(e) {
                        var $this = $(this);

                        // If this tab contains a URL link, let the natural link behavior happen
                        if ($this.find("a").length > 0) {
                            return true; // Allow default link behavior
                        }

                        e.preventDefault(); // Only prevent default for non-link tabs
                        var tabId = $this.data("tab");

                        console.log("Tab clicked:", tabId);

                        if ($this.hasClass("active")) return;

                        // Update tabs immediately
                        $(".sppb-tab").removeClass("active");
                        $this.addClass("active");

                        // Get current and target panes
                        var $currentPane = $(".sppb-tab-pane.active");
                        var $targetPane = $("#sppb-content-holiday-tab-" + tabId);

                        console.log("Target pane:", $targetPane.length ? "found" : "not found");

                        // Switch panes immediately
                        $currentPane.removeClass("active in");
                        $targetPane.addClass("active in");

                        // Load content if not already loaded
                        if (!$targetPane.data("loaded")) {
                            console.log("Loading content for tab:", tabId);

                            // Show loading spinner
                            $targetPane.html('<div class="loading-spinner" style="padding: 40px; text-align: center;"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>');

                            // Make AJAX request
                            var request = {
                                "option": "com_sppagebuilder",
                                "task": "ajax",
                                "addon": "holidaytabs",
                                "id": tabId,
                                "page_id": 273,
                                "method": "getTabContent"
                            };

                            console.log("AJAX Request:", request);

                            $.ajax({
                                type: "POST",
                                url: "/index.php",
                                dataType: "json",
                                data: request,
                                success: function(response) {
                                    console.log("AJAX Response:", response);

                                    if (response.success && response.data && response.data.status) {
                                        $targetPane.html(response.data.content);
                                        $targetPane.data("loaded", true);
                                        console.log("Content loaded successfully");
                                    } else {
                                        console.error("AJAX Response Error:", response);
                                        var errorMessage = response.message || "Unable to load content";
                                        console.error("Error message:", errorMessage);
                                        $targetPane.html('<div class="error-message" style="padding: 20px; color: #dc3545;">Error: ' + errorMessage + '</div>');
                                    }
                                },
                                error: function(xhr, status, error) {
                                    console.error("AJAX Error:", {
                                        status: status,
                                        error: error,
                                        response: xhr.responseText
                                    });

                                    try {
                                        var response = JSON.parse(xhr.responseText);
                                        console.error("Parsed error response:", response);
                                    } catch(e) {
                                        console.error("Raw error response:", xhr.responseText);
                                    }

                                    $targetPane.html('<div class="error-message" style="padding: 20px; color: #dc3545;">Error loading content. Please try again.</div>');
                                }
                            });
                        }
                    });
                });
            </script></div></div></div></div></div></div></div></div></div></div><section id="section-id-1743598653187" class="sppb-section why-choose-container" ><div class="sppb-row-container"><div class="sppb-row"><div class="sppb-col-md-12" id="column-wrap-id-1743598653188"><div id="column-id-1743598653188" class="sppb-column" ><div class="sppb-column-addons"><div id="section-id-1743598653189" class="sppb-section" ><div class="sppb-container-inner"><div class="sppb-row"><div class="sppb-col-md-12" id="column-wrap-id-1743598653190"><div id="column-id-1743598653190" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743598653191" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653191" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><h2 style="text-align: center;">Why choose EverTrek?</h2>
<p class="intro" style="text-align: center;">Here are just some of the reasons why thousands of adventurers choose us for their trekking experiences.</p></div></div></div></div><div id="sppb-addon-wrapper-1743598653192" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653192" class="sppb-hidden-md sppb-hidden-lg sppb-hidden-sm clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/images/awards/awardsx3.svg"  alt="Image" title=""   /></div></div></div></div></div></div></div></div><div class="sppb-col-md-3 sppb-col-xs-6" id="column-wrap-id-1743598653193"><div id="column-id-1743598653193" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743598653194" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653194" class="clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/templates/zenbase/icons/choose-rosette.svg"  alt="Image" title=""   /></div></div></div></div></div><div id="sppb-addon-wrapper-1743598653195" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653195" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block sppb-text-center "><div class="sppb-addon-content"><h3>Unmatched Expertise &amp; Experience</h3></div></div></div></div><div id="sppb-addon-wrapper-1743598653196" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653196" class="sppb-hidden-xs clearfix "     ><div class="sppb-addon sppb-addon-text-block sppb-text-center "><div class="sppb-addon-content"><p>Voted the UK's No.1 adventure trekking company, backed by over 30 years of industry knowledge and a passion for adventure. We make your journey seamless and unforgettable.</p></div></div></div></div></div></div></div><div class="sppb-col-md-3 sppb-col-xs-6" id="column-wrap-id-1743598653197"><div id="column-id-1743598653197" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743598653198" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653198" class="clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/templates/zenbase/icons/choose-clipboard.svg"  alt="Image" title=""   /></div></div></div></div></div><div id="sppb-addon-wrapper-1743598653199" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653199" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block sppb-text-center "><div class="sppb-addon-content"><h3>World-Class Support &amp; Preparation</h3></div></div></div></div><div id="sppb-addon-wrapper-1743598653200" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653200" class="sppb-hidden-xs clearfix "     ><div class="sppb-addon sppb-addon-text-block sppb-text-center "><div class="sppb-addon-content"><p>Our trip planners, mobile app, and expert advice from the moment you book ensure you’re ready to trek like a pro. With a 95% success rate, we don’t just talk the talk – we trek the trek.</p></div></div></div></div></div></div></div><div class="sppb-col-md-3 sppb-col-xs-6" id="column-wrap-id-1743598653201"><div id="column-id-1743598653201" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743598653202" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653202" class="clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/templates/zenbase/icons/choose-seedling.svg"  alt="Image" title=""   /></div></div></div></div></div><div id="sppb-addon-wrapper-1743598653203" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653203" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block sppb-text-center "><div class="sppb-addon-content"><h3>Local Guides &amp; Sustainability</h3></div></div></div></div><div id="sppb-addon-wrapper-1743598653204" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653204" class="sppb-hidden-xs clearfix "     ><div class="sppb-addon sppb-addon-text-block sppb-text-center "><div class="sppb-addon-content"><p>Our expert local guiding teams ensure money goes directly to the communities while we champion environmental sustainability with initiatives like fresh filtered water to reduce plastic waste.</p></div></div></div></div></div></div></div><div class="sppb-col-md-3 sppb-col-xs-6" id="column-wrap-id-1743598653205"><div id="column-id-1743598653205" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743598653206" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653206" class="clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/templates/zenbase/icons/choose-handshake.svg"  alt="Image" title=""   /></div></div></div></div></div><div id="sppb-addon-wrapper-1743598653207" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653207" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block sppb-text-center "><div class="sppb-addon-content"><h3>Community &amp; Camaraderie</h3></div></div></div></div><div id="sppb-addon-wrapper-1743598653208" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653208" class="sppb-hidden-xs clearfix "     ><div class="sppb-addon sppb-addon-text-block sppb-text-center "><div class="sppb-addon-content"><p>You’re not just booking an incredible adventure, you’re joining a vibrant community of EverTrekkers, where support, advice and summit high fives are always on tap. Together, we've got you covered every step of the way.</p></div></div></div></div></div></div></div><div class="sppb-col-md-12" id="column-wrap-id-1743598653209"><div id="column-id-1743598653209" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743598653210" class="sppb-addon-wrapper"><div id="sppb-addon-1743598653210" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><div style="text-align: center;"><a class="btn btn-primary" href="/about-us">Find out more about us <img src="/templates/zenbase/icons/chevron-right.svg" alt="" /></a></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></div></section><section id="section-id-1689260044302" class="sppb-section" ><div class="sppb-row-container"><div class="sppb-row"><div class="sppb-col-md-12" id="column-wrap-id-1689260044300"><div id="column-id-1689260044300" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739546099202" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099202" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  text-center text-md-start"><div class="sppb-addon-content"><h3 class="reviews-title">What customers say about us</h3>
<p class="intro">We’re Trek Prep experts and pride ourselves on providing the best experience</p></div></div></div></div><div id="sppb-addon-wrapper-1743751726697" class="sppb-addon-wrapper"><div id="sppb-addon-1743751726697" class="clearfix "     ><div class="sppb-addon sppb-addon-module "><div class="sppb-addon-content"><!-- <div class="embedsocial-hashtag" data-ref="816b4a7734f4188b5848cf0b506066f9b374b3ed"></div> <script> (function(d, s, id) { var js; if (d.getElementById(id)) {return;} js = d.createElement(s); js.id = id; js.src = "https://embedsocial.com/cdn/ht.js"; d.getElementsByTagName("head")[0].appendChild(js); }(document, "script", "EmbedSocialHashtagScript")); </script> -->

<div class="embedsocial-widget" data-ref="fc2d1a20d46e3a578af0152614a2d130" style=""></div> <script> (function(d, s, id) { var js; if (d.getElementById(id)) {return;} js = d.createElement(s); js.id = id; js.src = "https://embedsocial.com/cdn/aht.js"; d.getElementsByTagName("head")[0].appendChild(js); }(document, "script", "EmbedSocialWidgetScript")); </script>

<style>
          .embedsocial-widget {
          width: 100%;
          max-width: 100%;
          }
          @media screen and (min-width: 767px) {
          .embedsocial-widget {
            max-width: 1440px;
          }
          .embedsocial-widget iframe {
            margin: -2.5rem -1.25rem;
            width: 103%!important;
          }

</style></div></div></div></div></div></div></div></div></div></section><section id="section-id-1743597166109" class="sppb-section" ><div class="sppb-row-container"><div class="sppb-row"><div class="sppb-col-md-5" id="column-wrap-id-1743597166110"><div id="column-id-1743597166110" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743597166112" class="sppb-addon-wrapper"><div id="sppb-addon-1743597166112" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  text-center text-md-start"><div class="sppb-addon-content"><h2>Adventure Experts</h2>
<p class="intro">We pride ourselves on delivering world-class customer service for your life-changing, bucket-list adventures.</p>
<p>From choosing your trip to training tips, vaccinations, or gear advice, our team of Adventure Experts is here to make your journey smooth, stress-free, and unforgettable.</p></div></div></div></div></div></div></div><div class="sppb-col-md-1" id="column-wrap-id-1743597166111"><div id="column-id-1743597166111" class="sppb-column" ><div class="sppb-column-addons"></div></div></div><div class="sppb-col-md-6" id="column-wrap-id-1743597166113"><div id="column-id-1743597166113" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743597166115" class="sppb-addon-wrapper"><div id="sppb-addon-1743597166115" class="clearfix "     ><div class="sppb-addon sppb-addon-raw-html "><div class="sppb-addon-content"><img class="" src="/templates/zenbase/images/roundels_1.svg" /><img class="nudge-left" src="/templates/zenbase/images/roundels_2.svg" /></div></div></div></div><div id="sppb-addon-wrapper-1743597166116" class="sppb-addon-wrapper"><div id="sppb-addon-1743597166116" class="clearfix "     ><div class="sppb-addon sppb-addon-raw-html "><div class="sppb-addon-content"><div id="experts-container" class="container-fluid container-md text-center text-md-start">
<div class="row justify-content-between justify-content-md-center">
<div class="col-12 col-md-auto d-flex flex-wrap justify-content-between justify-content-md-center gap-md-4">
<a href="https://api.leadconnectorhq.com/widget/bookings/evertrekcalendar" class="text-white btn btn-primary bg-black border-black">Book a chat</a>
<a href="/about-us/meet-the-team" class="btn btn-primary btn-outline">About the team</a>
</div>
</div>
</div></div></div></div></div></div></div></div></div></div></section><section id="section-id-1739535159935" class="sppb-section" ><div class="sppb-row-container"><div class="sppb-row"><div class="sppb-col-md-12" id="column-wrap-id-1739535159927"><div id="column-id-1739535159927" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739546099214" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099214" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  text-center text-md-start"><div class="sppb-addon-content"><div class="community-intro">
<div class="community-intro-col">
<h2>The EverTrekker Community</h2>
<p class="community-intro">Voted the UK’s No.1 trekking and walking company, if you love the mountains, want to climb higher, trek further and tick Everest Base Camp, Kilimanjaro or Machu Picchu off your bucket list, come and join the community.</p>
</div>
<img class="awards mx-auto" src="/images/awards/awardsx3.svg" width="622" height="184" /></div></div></div></div></div></div></div></div><div class="sppb-col-md-4" id="column-wrap-id-1739535159928"><div id="column-id-1739535159928" class="sppb-column h-100 comm-col-1" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739546099220" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099220" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  text-center text-md-start"><div class="sppb-addon-content"><h3 class="sub-title">Tuesday Tune-In</h3>
<p class="sub-copy">Our Tuesday Tune-In hosted by Andy, Dave &amp; the EverTrek Yetis, has been an absolute hoot to present! This is where not only do we answer ALL your questions on hot topics on kit, fitness, top tips, new trips and everything in between - we also have an absolute blast! Make sure to tune-in every Tuesday at 12.30pm over on our <a href="https://www.facebook.com/evertrekuk">FB Page</a> and say 'hi'!</p></div></div></div></div><div id="sppb-addon-wrapper-1739546099228" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099228" class="clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><a  href="https://www.facebook.com/evertrekuk"><img class="sppb-img-responsive in-viewport" data-vp_path="/templates/zenbase/images/image.png"  alt="Image" title=""   /></a></div></div></div></div></div></div></div></div><div class="sppb-col-md-4" id="column-wrap-id-1739535159929"><div id="column-id-1739535159929" class="sppb-column h-100 comm-col-2" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739546099223" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099223" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  text-center text-md-start"><div class="sppb-addon-content"><h3 class="sub-title">Mountain Malarkey Podcast</h3>
<p class="sub-copy">Curious about high-altitude trekking and climbing? Join EverTrek Yeti’s Andy and Dave each week on the Mountain Malarkey podcast. Get stories, interviews, and practical tips on training, gear, nutrition, mindset, and more to fuel your next big adventure!</p>
<p><a class="btn btn-primary" href="https://open.spotify.com/show/1owSd6OijgCBrr963QVQYb?si=defbede7e1744743&amp;nd=1&amp;dlsi=e0f4de5382994441" target="_blank" rel="noopener noreferrer">Listen now</a></p></div></div></div></div><div id="sppb-addon-wrapper-1739546099231" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099231" class="clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><a rel="noopener noreferrer" target="_blank" href="https://open.spotify.com/show/1owSd6OijgCBrr963QVQYb?si=defbede7e1744743&nd=1&dlsi=e0f4de5382994441"><img class="sppb-img-responsive in-viewport" data-vp_path="/templates/zenbase/images/mountain_malarkey.webp"  alt="Image" title=""   /></a></div></div></div></div></div></div></div></div><div class="sppb-col-md-4" id="column-wrap-id-1739535159930"><div id="column-id-1739535159930" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739546099234" class="sppb-addon-wrapper"><div id="sppb-addon-1739546099234" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  text-center text-md-start"><div class="sppb-addon-content"><h3 class="sub-title">Join our Facebook group <a class="btn btn-primary fb-join" href="https://www.facebook.com/groups/evertrekHAHT">Join</a></h3>
<p class="sub-copy">Join our incredible Facebook community of 15,000+ trekkers who’ve already conquered epic adventures like Everest Base Camp, Kilimanjaro, and Machu Picchu. Or 'Like' and follow our page for all the latest updates and inspiration!</p></div></div></div></div><div id="sppb-addon-wrapper-1743751726690" class="sppb-addon-wrapper"><div id="sppb-addon-1743751726690" class="clearfix "     ><div class="sppb-addon sppb-addon-module "><div class="sppb-addon-content"><!-- Elfsight Facebook Feed | EverTrek Facebook Feed -->
<script src="https://static.elfsight.com/platform/platform.js" async></script>
<div class="elfsight-app-35ce027b-03fc-48fc-8728-3e1f291ca453" data-elfsight-app-lazy></div>

<style>
.sppb-column-addons {
  display:flex!important;
  flex-direction: column;
}

#sppb-addon-wrapper-1739546099234 {
  flex-grow: 0; 
}

#sppb-addon-wrapper-1743751726690 {
  flex-grow: 1!important; 
  align-items: flex-end;
  display: flex;
  flex-direction: row;
  width: 100%;

#sppb-addon-wrapper-1743751726690
}

#sppb-addon-1743751726690,
#sppb-addon-1743751726690 .sppb-addon-raw-html,
#sppb-addon-1743751726690 .sppb-addon-content,
#sppb-addon-1743751726690 .eapps-facebook-feed-small,
#sppb-addon-1743751726690 .eapps-facebook-feed-extra-small,
#sppb-addon-1743751726690 .eapps-facebook-feed,
#sppb-addon-1743751726690 .eapps-facebook-feed-container,
#sppb-addon-1743751726690 .eapps-facebook-feed-inner,
#sppb-addon-1743751726690 .sppb-addon-module
 {
  height: 100%;
max-height: 400px;
width:100%;
margin-top: 15px;
}

.eapps-facebook-feed-content-container {
  flex-grow:1; 
}

/* @media screen and (max-width: 991px) {
#sppb-addon-1743751726690,
.sppb-addon-raw-html,
.sppb-addon-content,
.eapps-facebook-feed-small,
.eapps-facebook-feed-extra-small,
.eapps-facebook-feed,
.eapps-facebook-feed-container,
.eapps-facebook-feed-inner,
#sppb-addon-1743751726690 .sppb-addon-module {
 {
  height: 100%;
max-height: 400px;
}
}
*/
</style>
</div></div></div></div></div></div></div></div></div></section><section id="section-id-1739546099341" class="sppb-section" ><div class="sppb-row-container"><div class="sppb-row"><div class="sppb-col-md-12" id="column-wrap-id-1739546099340"><div id="column-id-1739546099340" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1743751726685" class="sppb-addon-wrapper"><div id="sppb-addon-1743751726685" class="clearfix "     ><div class="sppb-addon sppb-addon-module "><div class="sppb-addon-content"><div class="modal fade" id="videoLightbox" tabindex="-1" aria-labelledby="videoLightboxLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-xl">
    <div class="modal-content">
      <div class="modal-body">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="ratio ratio-16x9">
         <iframe
  data-src="https://www.youtube.com/embed/l1hU7Od2X-w?enablejsapi=1"
  allowfullscreen
  allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
</iframe>
        </div>
        <div class="mt-3">
          <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-2">
            <!-- Column 1 -->
            <div class="col">
              <div class="row g-2">
                <div class="col-2 px-md-2 col-md-3">
                  <img class="sppb-img-responsive " alt="Image" title="" style=""
                    src="/templates/zenbase/icons/choose-rosette.svg">
                </div>
                <div class="col-10 px-md-2 col-md-9">
                  <p class="mb-0">Unmatched Expertise and Extensive Experience</p>
                </div>
              </div>
            </div>
            <!-- Column 2 -->
            <div class="col">
              <div class="row g-2">
                <div class="col-2 px-md-2 col-md-3">
                  <img class="sppb-img-responsive " alt="Image" title="" style=""
                    src="/templates/zenbase/icons/choose-clipboard.svg">
                </div>
                <div class="col-10 px-md-2 col-md-9">
                  <p class="mb-0">Comprehensive Preparation and Support</p>
                </div>
              </div>
            </div>
            <!-- Column 3 -->
            <div class="col">
              <div class="row g-2">
                <div class="col-2 px-md-2 col-md-3">
                  <img class="sppb-img-responsive " alt="Image" title="" style=""
                    src="/templates/zenbase/icons/choose-seedling.svg">
                </div>
                <div class="col-10 px-md-2 col-md-9">
                  <p class="mb-0">Local Guides and Environmental Responsibility</p>
                </div>
              </div>
            </div>
            <!-- Column 4 -->
            <div class="col">
              <div class="row g-2">
                <div class="col-2 px-md-2 col-md-3">
                  <img class="sppb-img-responsive " alt="Image" title="" style=""
                    src="/templates/zenbase/icons/choose-handshake.svg">
                </div>
                <div class="col-10 px-md-2 col-md-9">
                  <p class="mb-0">World-Class Customer Service and Financial Flexibility</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    #videoLightbox .ratio {
      background-color: black;
      border-radius: 8px;
      overflow: hidden;
    }

    #videoLightbox .ratio iframe {
      z-index: 2;
    }

    #videoLightbox .modal-xl {
      max-width: 70%;
      width: 70%;
    }

    #videoLightbox .modal-body {
      padding: 0;
      background-color: transparent;
      position: relative;
    }

    #videoLightbox .btn-close {
      position: absolute;
      right: 15px;
      top: 15px;
      color: white;
      font-size: 1.25rem;
      opacity: 1;
      z-index: 3;
      background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
    }

    #videoLightbox .modal-content {
      background-color: transparent!important;
      border: none;
      font-size: 14.5px;
      line-height: normal;
      font-weight: bold;
    }

    #videoLightbox .mt-3 {
      background-color: transparent;
      color: white;
    }

    #videoLightbox .row:not(.justify-content-center) .col-8 {
      flex: 0 0 66.666667%;
      max-width: 66.666667%;
    }

    #videoLightbox .row:not(.justify-content-center) .col-4 {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }

    #videoLightbox .row:not(.justify-content-center) .col-4.col-md-12 {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }

    @media (max-width: 767px) {
      #videoLightbox .modal-dialog {
        width: 95%;
        max-width: 95%;
        margin: 0 auto;
      }

      #videoLightbox .video-container {
        width: 100%;
      }

      #videoLightbox .modal-xl {
        max-width: 95%;
        width: 95%;
      }

      #videoLightbox .col-10.px-md-2.col-md-9 {
        padding-left: 20px;
        padding-right: 30px;
        display: flex;
        align-items: center;
      }

      #videoLightbox .col-2.px-md-2.col-md-3 {
        padding-right: 0;
      }

      #videoLightbox .col-2.px-md-2.col-md-3 img {
        width: 42px;
        max-width: 42px;
        height: auto;
      }
    }

    #videoLightbox .mt-3>.row {
      margin-left: 0;
      margin-right: 0;
    }
    #videoLightbox .col {
      margin-top: 15px;
    }

    #videoLightbox .modal-content img {
      height: 63px;
      width: auto;
    }

    #videoLightbox .col-2.px-md-2.col-md-3 {
      display: flex;
      justify-content: flex-end;
      padding-right: 10px !important;
      padding-left: 0;
    }

    @media screen and (min-width: 768px) {

    }
  </style>
<script>
  document.addEventListener('DOMContentLoaded', function() {
    var videoLightbox = document.getElementById('videoLightbox');
    if (!videoLightbox) return;

    var iframe = videoLightbox.querySelector('iframe');
    if (!iframe) return;

    var videoSrc = iframe.getAttribute('data-src');

    // When modal is shown, set the src to load the video
    videoLightbox.addEventListener('show.bs.modal', function () {
      if (iframe && videoSrc) {
        iframe.setAttribute('src', videoSrc);
      }
    });

    // When modal is hidden, remove the src to unload the video
    videoLightbox.addEventListener('hidden.bs.modal', function () {
      if (iframe) {
        iframe.removeAttribute('src');
      }
    });
  });
</script></div></div></div></div></div></div></div></div></div></section>
			</div>
</div>
          </main>

        </div>
      </div>

<style>
 /* GLOBAL */

                /* OFFER BAR */
                .light-link { text-decoration:none; border-bottom: 1px solid rgba(0,0,0,.25); padding-bottom:0; margin-bottom:0;  }
                .fg-black { color: black; }
                .bg-yellow { background-color: #ffc105; }
                .zen-header strong { font-family: "IBM Plex Sans Semibold"; font-weight: 400; }
                /*.zen-body__main { padding: 36px 0 0; }*/


                /* MOBILE BAR */

                .mobile-bar {
                  position:fixed; bottom: 0; left:0; right: 0; display:flex; align-items: center;justify-content:space-around;
                  box-shadow: 0 1px 10px 0 rgba(38, 38, 38, 0.3);
                  background-color: rgba(255, 255, 255, 1);
                  transition:bottom .1s ease-in-out;
                  z-index: 9999999999;
                }
                .mobile-bar div {
                  display: flex; flex-direction: column; align-items: center;
                }
                .mobile-bar a { text-decoration: none; }
                .mobile-bar a:focus svg, .mobile-bar a:active svg { color: #999; }
                .mobile-bar svg { height: 40px; color: #525252; transition: .25s color; }
                .mobile-bar span { color: #8c8c8c; display:block; margin-top: 5px; }
                .mobile-bar a[disabled] { opacity: 0.5; pointer-events: none;}
                .mobile-bar-show { display:block }
                .mobile-bar-hide { display: none; bottom:-100px }
</style>

      <div class="mobile-bar py-3 d-flex d-sm-none">
            <a class="mobile-brochure-button" href="/about-us/download-our-adventure-brochure">
              <div><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 83 61"><g fill="currentColor"><path d="M52.311 52.038a.954.954 0 0 1-.79 1.127c-7.887 1.113-11.887 3.347-14.38 5.936l-.48.193a.584.584 0 0 1-.481-.579V11.023c0-.579.288-1.253.77-1.638a20.215 20.215 0 0 1 4.522-2.891 51.908 51.908 0 0 1 21.746-3.757l.098-.001c2.427 0 4.425 2 4.425 4.431v24.915a.955.955 0 0 1-.89.95c-8.255.59-14.834 7.57-14.834 15.95 0 1.026.098 2.05.294 3.056ZM26.269 6.397a20.207 20.207 0 0 1 4.618 2.892c.482.481.77 1.155.77 1.734v47.691c0 .29-.288.58-.578.58-.192 0-.288 0-.48-.194-5.966-5.685-16.07-6.647-26.077-6.647C1.924 52.453 0 50.428 0 47.634V7.168c0-1.445.77-2.89 1.924-3.66a5.761 5.761 0 0 1 2.694-.771c9.623 0 16.647 1.252 21.747 3.66h-.096ZM66.2 50.449v-6.283l-.002-.071c0-.74.608-1.349 1.349-1.349a1.36 1.36 0 0 1 1.346 1.42V50.5l2.235-2.19a1.35 1.35 0 0 1 .942-.383c.739 0 1.347.608 1.347 1.347 0 .363-.146.71-.405.965L68.49 54.67a1.358 1.358 0 0 1-1.895-.01l-4.426-4.433a1.349 1.349 0 0 1-.395-.954c0-.739.608-1.347 1.348-1.347.357 0 .7.142.953.394l2.125 2.13Z"/><path d="M67.548 36.94c6.571 0 11.98 5.416 11.98 11.996s-5.409 11.996-11.98 11.996c-6.571 0-11.98-5.415-11.98-11.996 0-6.58 5.408-11.996 11.98-11.996Zm0 2.698c-5.094 0-9.286 4.197-9.286 9.298 0 5.1 4.192 9.298 9.286 9.298 5.094 0 9.285-4.197 9.285-9.298 0-5.1-4.191-9.298-9.285-9.298Z"/></g></svg><span>Brochure</span></div>
            </a>
            <a class="mobile-chat-button" href="#" disabled>
              <div><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M398 81.84A227.4 227.4 0 0 0 255.82 32C194.9 32 138 55.47 95.46 98.09C54.35 139.33 31.82 193.78 32 251.37a215.66 215.66 0 0 0 35.65 118.76l.19.27c.28.41.57.82.86 1.22s.65.92.73 1.05l.22.4c1.13 2 2 4.44 1.23 6.9l-18.42 66.66a29.13 29.13 0 0 0-1.2 7.63A25.69 25.69 0 0 0 76.83 480a29.44 29.44 0 0 0 10.45-2.29l67.49-24.36l.85-.33a14.75 14.75 0 0 1 5.8-1.15a15.12 15.12 0 0 1 5.37 1c1.62.63 16.33 6.26 31.85 10.6c12.9 3.6 39.74 9 60.77 9c59.65 0 115.35-23.1 156.83-65.06C457.36 365.77 480 310.42 480 251.49a213.5 213.5 0 0 0-4.78-45c-10.34-48.62-37.76-92.9-77.22-124.65M160 288a32 32 0 1 1 32-32a32 32 0 0 1-32 32m96 0a32 32 0 1 1 32-32a32 32 0 0 1-32 32m96 0a32 32 0 1 1 32-32a32 32 0 0 1-32 32"/></svg><span>Live Chat</span></div>
            </a>
            <a class="mobile-callback-button" href="/call-me-back">
              <div><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 512 512"><path fill="currentColor" d="M391 480c-19.52 0-46.94-7.06-88-30c-49.93-28-88.55-53.85-138.21-103.38C116.91 298.77 93.61 267.79 61 208.45c-36.84-67-30.56-102.12-23.54-117.13C45.82 73.38 58.16 62.65 74.11 52a176.3 176.3 0 0 1 28.64-15.2c1-.43 1.93-.84 2.76-1.21c4.95-2.23 12.45-5.6 21.95-2c6.34 2.38 12 7.25 20.86 16c18.17 17.92 43 57.83 52.16 77.43c6.15 13.21 10.22 21.93 10.23 31.71c0 11.45-5.76 20.28-12.75 29.81c-1.31 1.79-2.61 3.5-3.87 5.16c-7.61 10-9.28 12.89-8.18 18.05c2.23 10.37 18.86 41.24 46.19 68.51s57.31 42.85 67.72 45.07c5.38 1.15 8.33-.59 18.65-8.47c1.48-1.13 3-2.3 4.59-3.47c10.66-7.93 19.08-13.54 30.26-13.54h.06c9.73 0 18.06 4.22 31.86 11.18c18 9.08 59.11 33.59 77.14 51.78c8.77 8.84 13.66 14.48 16.05 20.81c3.6 9.53.21 17-2 22c-.37.83-.78 1.74-1.21 2.75a176.49 176.49 0 0 1-15.29 28.58c-10.63 15.9-21.4 28.21-39.38 36.58A67.42 67.42 0 0 1 391 480"/></svg><span>Call me back</span></div>
            </a>
            </div>

    <!------------------------------------------------------------------------------
    // Footer
    //----------------------------------------------------------------------------->
    
        <!------------------------------------------------------------------------------
    // Footer
    //----------------------------------------------------------------------------->
    <footer class="zen-footer">
              <div class="mod-sppagebuilder  sp-page-builder" data-module_id="163">
	<div class="page-content">
		<div id="section-id-1739968391050" class="sppb-section" ><div class="sppb-container-inner"><div class="sppb-row"><div class="sppb-col-md-12" id="column-wrap-id-1739968391049"><div id="column-id-1739968391049" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739968559489" class="sppb-addon-wrapper"><div id="sppb-addon-1739968559489" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  "><div class="sppb-addon-content"><h3 class="sub-title" style="text-align: center;">Our Trusted Partners</h3></div></div><style type="text/css">#sppb-addon-wrapper-1739968559489 {
margin:0px 0px 0px 0px;}
#sppb-addon-1739968559489 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1739968559489 {
}
#sppb-addon-1739968559489.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739968559489 {}}@media (max-width: 767px) {#sppb-addon-1739968559489 {}}</style><style type="text/css">@media (min-width: 768px) and (max-width: 991px) {}@media (max-width: 767px) {}</style></div></div></div></div></div><div class="sppb-col-md-12" id="column-wrap-id-1739968981671"><div id="column-id-1739968981671" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739968559492" class="sppb-addon-wrapper"><div id="sppb-addon-1739968559492" class="sppb-hidden-xs clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/images/2025/05/14/desktop-partnership-logo-banner-o.png"  alt="Image" title=""   /></div></div></div><style type="text/css">#sppb-addon-wrapper-1739968559492 {
margin:0px 0px 0px 0px;}
#sppb-addon-1739968559492 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1739968559492 {
}
#sppb-addon-1739968559492.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739968559492 {}}@media (max-width: 767px) {#sppb-addon-1739968559492 {}}</style><style type="text/css">#sppb-addon-1739968559492 img{}@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739968559492 img{}}@media (max-width: 767px) {#sppb-addon-1739968559492 img{}}</style></div></div><div id="sppb-addon-wrapper-1739987189474" class="sppb-addon-wrapper"><div id="sppb-addon-1739987189474" class="sppb-hidden-md sppb-hidden-lg sppb-hidden-sm clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/images/2025/05/14/mobile-partnership-logo-banner-o.png"  alt="Image" title=""   /></div></div></div><style type="text/css">#sppb-addon-wrapper-1739987189474 {
margin:0px 0px 0px 0px;}
#sppb-addon-1739987189474 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1739987189474 {
}
#sppb-addon-1739987189474.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739987189474 {}}@media (max-width: 767px) {#sppb-addon-1739987189474 {}}</style><style type="text/css">#sppb-addon-1739987189474 img{}@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739987189474 img{}}@media (max-width: 767px) {#sppb-addon-1739987189474 img{}}</style></div></div></div></div></div></div></div></div><div id="section-id-1739968391060" class="sppb-section" ><div class="sppb-container-inner"><div class="sppb-row"><div class="sppb-col-md-4" id="column-wrap-id-1739968391051"><div id="column-id-1739968391051" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739971503842" class="sppb-addon-wrapper"><div id="sppb-addon-1739971503842" class="clearfix "     ><div class="sppb-addon sppb-addon-text-block  text-center text-md-start"><div class="sppb-addon-content"><h5><a href="mailto:<EMAIL>"><EMAIL></a><br /><a href="tel:+442929003216">02929 003 216</a></h5>
<p><a class="btn btn-primary" href="https://api.leadconnectorhq.com/widget/bookings/evertrekcalendar"><img src="/templates/zenbase/icons/phone_iphone.svg" alt="" /> Schedule a callback</a></p></div></div><style type="text/css">#sppb-addon-wrapper-1739971503842 {
margin:0px 0px 30px 0px;}
#sppb-addon-1739971503842 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1739971503842 {
}
#sppb-addon-1739971503842.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739971503842 {}}@media (max-width: 767px) {#sppb-addon-1739971503842 {}}#sppb-addon-1739971503842 p,#sppb-addon-1739971503842 
a:not(.btn){color:white;text-decoration:none;}#sppb-addon-1739971503842 h5{margin-bottom:20px;}</style><style type="text/css">@media (min-width: 768px) and (max-width: 991px) {}@media (max-width: 767px) {}</style></div></div></div></div></div><div class="sppb-col-md-4" id="column-wrap-id-1739968391052"><div id="column-id-1739968391052" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739972157935" class="sppb-addon-wrapper"><div id="sppb-addon-1739972157935" class="clearfix "     ><div class="sppb-addon sppb-addon-module text-center text-md-start"><div class="sppb-addon-content"><div class="zen-menu-container">

<!-- First Level Items -->
<ul class="zen-list zen-list--menu menu">
		<li class="nav-item item-126" data-item-id="126">
		<!-- Using anchor for clickable item: About EverTrek (ID: 126) with href: /about -->
<a class="zen-link nav-link" href="/about" >About EverTrek</a>	</li>
		<li class="nav-item item-769" data-item-id="769">
		<!-- Using anchor for clickable item: Request a Brochure (ID: 769) with href: /download-our-adventure-brochure -->
<a class="zen-link nav-link" href="/download-our-adventure-brochure" >Request a Brochure</a>	</li>
		<li class="nav-item item-531" data-item-id="531">
		<!-- Using anchor for clickable item: Get in Touch (ID: 531) with href: contact -->
<a class="zen-link nav-link" href="/contact" >Get in Touch</a>	</li>
		<li class="nav-item item-770" data-item-id="770">
		<!-- Using anchor for clickable item: Knowledge Centre (ID: 770) with href: /knowledge-centre -->
<a class="zen-link nav-link" href="/knowledge-centre" >Knowledge Centre</a>	</li>
		<li class="nav-item item-774" data-item-id="774">
		<!-- Using anchor for clickable item: EverTrek Reviews (ID: 774) with href: /evertrek-reviews -->
<a class="zen-link nav-link" href="/evertrek-reviews" >EverTrek Reviews</a>	</li>
		<li class="nav-item item-771" data-item-id="771">
		<!-- Using anchor for clickable item: Trip Search (ID: 771) with href: /search -->
<a class="zen-link nav-link" href="/search" >Trip Search</a>	</li>
		<li class="nav-item item-773" data-item-id="773">
		<!-- Using anchor for clickable item: Privacy Policy (ID: 773) with href: /privacy-policy -->
<a class="zen-link nav-link" href="/privacy-policy" >Privacy Policy</a>	</li>
		<li class="nav-item item-772" data-item-id="772">
		<!-- Using anchor for clickable item: Terms and Conditions (ID: 772) with href: /terms-and-conditions -->
<a class="zen-link nav-link" href="/terms-and-conditions" >Terms and Conditions</a>	</li>
		<li class="nav-item item-533" data-item-id="533">
		<!-- Using anchor for clickable item: EverTrek Merch Store (ID: 533) with href: https://evertrek-equipment.myshopify.com/collections/all -->
<a class="zen-link nav-link" href="https://evertrek-equipment.myshopify.com/collections/all" >EverTrek Merch Store</a>	</li>
		<li class="nav-item item-590" data-item-id="590">
		<!-- Using anchor for clickable item: Call me Back (ID: 590) with href: /call-me-back -->
<a class="zen-link nav-link" href="/call-me-back" >Call me Back</a>	</li>
</ul>

<!-- Second Level Items -->
<div class="zen-list--menu-second-level">
	</div>

<!-- Third Level Items -->
<div class="zen-list--menu-third-level">
	</div>

<div class="zen-list--menu-content"></div>
</div></div></div><style type="text/css">#sppb-addon-wrapper-1739972157935 {
margin:0px 0px 30px 0px;}
#sppb-addon-1739972157935 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1739972157935 {
}
#sppb-addon-1739972157935.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739972157935 {}}@media (max-width: 767px) {#sppb-addon-1739972157935 {}}#sppb-addon-1739972157935 a{color:white;padding:0.4rem 1rem;}</style></div></div></div></div></div><div class="sppb-col-md-4" id="column-wrap-id-1739968391053"><div id="column-id-1739968391053" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739985090171" class="sppb-addon-wrapper"><div id="sppb-addon-1739985090171" class="clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center col-11 col-md-12 mx-auto"><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><a rel="noopener noreferrer" target="_blank" href="https://www.abtot.com/abtot-is-delighted-to-welcome-bucket-list-adventure-travel-limited-t-as-evertrek-as-a-new-member-number-5534/"><img class="sppb-img-responsive in-viewport" data-vp_path="/templates/zenbase/icons/abtot-block.svg"  alt="Image" title=""   /></a></div></div></div><style type="text/css">#sppb-addon-wrapper-1739985090171 {
margin:0px 0px 30px 0px;}
#sppb-addon-1739985090171 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1739985090171 {
}
#sppb-addon-1739985090171.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739985090171 {}}@media (max-width: 767px) {#sppb-addon-1739985090171 {}}#sppb-addon-1739985090171 .sppb-addon-single-image-container{width:100%;}</style><style type="text/css">#sppb-addon-1739985090171 img{}@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739985090171 img{}}@media (max-width: 767px) {#sppb-addon-1739985090171 img{}}</style></div></div></div></div></div><div class="sppb-col-md-12" id="column-wrap-id-1739968391054"><div id="column-id-1739968391054" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1741716221294" class="sppb-addon-wrapper"><div id="sppb-addon-1741716221294" class="sppb-hidden-md sppb-hidden-lg sppb-hidden-sm clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/images/awards/awardsx3.svg"  alt="Image" title=""   /></div></div></div><style type="text/css">#sppb-addon-wrapper-1741716221294 {
margin:0px 0px 0px 0px;}
#sppb-addon-1741716221294 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1741716221294 {
}
#sppb-addon-1741716221294.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1741716221294 {}}@media (max-width: 767px) {#sppb-addon-1741716221294 {}#sppb-addon-wrapper-1741716221294 {margin-top: 0px;margin-right: 0px;margin-bottom: 30px;margin-left: 0px;}}#sppb-addon-1741716221294 img{width:100%;}</style><style type="text/css">#sppb-addon-1741716221294 img{}@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1741716221294 img{}}@media (max-width: 767px) {#sppb-addon-1741716221294 img{}}</style></div></div><div id="section-id-1741719066863" class="sppb-section" ><div class="sppb-container-inner"><div class="sppb-row"><div class="sppb-col-md-7" id="column-wrap-id-1741719066864"><div id="column-id-1741719066864" class="sppb-column" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739971989911" class="sppb-addon-wrapper"><div id="sppb-addon-1739971989911" class="sppb-hidden-xs clearfix "     ><div class="sppb-addon sppb-addon-text-block  text-center text-md-start"><div class="sppb-addon-content"><p class="footer-social"><a href="https://www.facebook.com/evertrekuk/" target="_blank" rel="noopener noreferrer"><img src="/templates/zenbase/icons/button-facebook.svg" alt="Facebook" /></a> <a href="https://www.instagram.com/evertrekuk/" target="_blank" rel="noopener noreferrer"><img src="/templates/zenbase/icons/button-instagram.svg" alt="Instagram" /></a> <a href="https://www.youtube.com/@EverTrekUK" target="_blank" rel="noopener noreferrer"><img src="/templates/zenbase/icons/button-youtube.svg" alt="Youtube" /></a> <a href="https://twitter.com/evertrekuk" target="_blank" rel="noopener noreferrer"><img src="/templates/zenbase/icons/button-twitter.svg" alt="Twitter" /></a></p>
<p>©2024 - EverTrek - Bucket List Adventure Travel Limited UK no. 10586281 - <a href="/about-us/privacy-policy">Privacy Policy</a></p></div></div><style type="text/css">#sppb-addon-wrapper-1739971989911 {
margin:0px 0px 0px 0px;}
#sppb-addon-1739971989911 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1739971989911 {
}
#sppb-addon-1739971989911.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739971989911 {}}@media (max-width: 767px) {#sppb-addon-1739971989911 {}}#sppb-addon-1739971989911 p,#sppb-addon-1739971989911  a{font-size:12px;color:white;text-decoration:none;}#sppb-addon-1739971989911 a{margin:5px;}#sppb-addon-1739971989911 .footer-social{margin-bottom:15px;}#sppb-addon-1739971989911 p:last-of-type{margin-bottom:0;}</style><style type="text/css">@media (min-width: 768px) and (max-width: 991px) {}@media (max-width: 767px) {}</style></div></div><div id="sppb-addon-wrapper-1744830644839" class="sppb-addon-wrapper"><div id="sppb-addon-1744830644839" class="sppb-hidden-md sppb-hidden-lg sppb-hidden-sm clearfix "     ><div class="sppb-addon sppb-addon-text-block  text-center text-md-start"><div class="sppb-addon-content"><p class="footer-social"><a href="https://www.facebook.com/evertrekuk/"><img src="/templates/zenbase/icons/button-facebook.svg" alt="Facebook" /></a> <a href="https://www.instagram.com/evertrekuk/"><img src="/templates/zenbase/icons/button-instagram.svg" alt="Instagram" /></a> <a href="https://www.youtube.com/@EverTrekUK"><img src="/templates/zenbase/icons/button-youtube.svg" alt="Youtube" /></a> <a href="https://twitter.com/evertrekuk"><img src="/templates/zenbase/icons/button-twitter.svg" alt="Twitter" /></a></p>
<p>©2024 - EverTrek - Bucket List Adventure Travel Limited UK no. 10586281 - <a href="/about-us/privacy-policy">Privacy Policy</a></p></div></div><style type="text/css">#sppb-addon-wrapper-1744830644839 {
margin:0px 0px 0px 0px;}
#sppb-addon-1744830644839 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1744830644839 {
}
#sppb-addon-1744830644839.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1744830644839 {}}@media (max-width: 767px) {#sppb-addon-1744830644839 {}}#sppb-addon-1744830644839 p,#sppb-addon-1744830644839  a{font-size:12px;color:white;text-decoration:none;}#sppb-addon-1744830644839 a{margin:5px;}#sppb-addon-1744830644839 .footer-social{margin-bottom:15px;}#sppb-addon-1744830644839 p:last-of-type{margin-bottom:0;}</style><style type="text/css">@media (min-width: 768px) and (max-width: 991px) {}@media (max-width: 767px) {}</style></div></div></div></div></div><div class="sppb-col-md-5 sppp-column-vertical-align" id="column-wrap-id-1741719066867"><div id="column-id-1741719066867" class="sppb-column sppb-align-items-bottom" ><div class="sppb-column-addons"><div id="sppb-addon-wrapper-1739972034845" class="sppb-addon-wrapper"><div id="sppb-addon-1739972034845" class="sppb-hidden-xs clearfix "     ><div class="sppb-addon sppb-addon-single-image sppb-text-center "><div class="sppb-addon-content"><div class="sppb-addon-single-image-container"><img class="sppb-img-responsive in-viewport" data-vp_path="/images/awards/awardsx3.svg"  alt="Image" title=""   /></div></div></div><style type="text/css">#sppb-addon-wrapper-1739972034845 {
margin:0px 0px 0px 0px;}
#sppb-addon-1739972034845 {
	box-shadow: 0 0 0 0 #ffffff;
}
#sppb-addon-1739972034845 {
}
#sppb-addon-1739972034845.sppb-element-loaded {
}
@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739972034845 {}}@media (max-width: 767px) {#sppb-addon-1739972034845 {}}#sppb-addon-1739972034845 img{width:100%;max-width:402px;}#sppb-addon-1739972034845 .sppb-addon-single-image-container{width:100%;}</style><style type="text/css">#sppb-addon-1739972034845 img{}@media (min-width: 768px) and (max-width: 991px) {#sppb-addon-1739972034845 img{}}@media (max-width: 767px) {#sppb-addon-1739972034845 img{}}</style></div></div></div></div></div></div></div></div><style type="text/css">.sp-page-builder .page-content #section-id-1739968391050{padding-top:35px;padding-right:0px;padding-bottom:35px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-image:url(/templates/zenbase/images/AdobeStock_310360177_w_lg.webp);background-repeat:no-repeat;background-size:cover;background-attachment:scroll;background-position:50% 50%;box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1739968391050 > .sppb-row-overlay {mix-blend-mode:normal;}#column-id-1739968391049{box-shadow:0 0 0 0 #fff;}.sp-page-builder .page-content #section-id-1739968391060{padding-top:50px;padding-right:0px;padding-bottom:50px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-color:#000000;background-image:url(/templates/zenbase/images/AdobeStock_434335984_lg_2.webp);background-repeat:no-repeat;background-size:cover;background-attachment:scroll;background-position:50% 50%;box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1739968391060 > .sppb-row-overlay {mix-blend-mode:normal;}#column-id-1739968391051{box-shadow:0 0 0 0 #fff;}#column-id-1739968391052{box-shadow:0 0 0 0 #fff;}#column-id-1739968391053{box-shadow:0 0 0 0 #fff;}#column-id-1739968391054{box-shadow:0 0 0 0 #fff;}.sp-page-builder .page-content #section-id-1741719066863{padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;box-shadow:0 0 0 0 #ffffff;}#column-id-1741719066864{box-shadow:0 0 0 0 #fff;}</style></div></div></div></div></div></div><style type="text/css">.sp-page-builder .page-content #section-id-1739968391050{padding-top:35px;padding-right:0px;padding-bottom:35px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-image:url(/templates/zenbase/images/AdobeStock_310360177_w_lg.webp);background-repeat:no-repeat;background-size:cover;background-attachment:scroll;background-position:50% 50%;box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1739968391050 > .sppb-row-overlay {mix-blend-mode:normal;}#column-id-1739968391049{box-shadow:0 0 0 0 #fff;}.sp-page-builder .page-content #section-id-1739968391060{padding-top:50px;padding-right:0px;padding-bottom:50px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;background-color:#000000;background-image:url(/templates/zenbase/images/AdobeStock_434335984_lg_2.webp);background-repeat:no-repeat;background-size:cover;background-attachment:scroll;background-position:50% 50%;box-shadow:0 0 0 0 #ffffff;}.sp-page-builder .page-content #section-id-1739968391060 > .sppb-row-overlay {mix-blend-mode:normal;}#column-id-1739968391051{box-shadow:0 0 0 0 #fff;}#column-id-1739968391052{box-shadow:0 0 0 0 #fff;}#column-id-1739968391053{box-shadow:0 0 0 0 #fff;}#column-id-1739968391054{box-shadow:0 0 0 0 #fff;}.sp-page-builder .page-content #section-id-1741719066863{padding-top:0px;padding-right:0px;padding-bottom:0px;padding-left:0px;margin-top:0px;margin-right:0px;margin-bottom:0px;margin-left:0px;box-shadow:0 0 0 0 #ffffff;}#column-id-1741719066864{box-shadow:0 0 0 0 #fff;}</style>	</div>
</div>
<style>

	.campaign-clock {
		/* filter: drop-shadow( 0px 0px 3px rgba(0, 0, 0, .7));*/
        text-align: center;
    }
	.campaign-clock .digit {
	    display:inline-block;
        /* background-color: black;*/
        padding: 0 5px;
        margin: 0 5px;
        border-radius: 5px;
    }
   /* .delimiter { width: 20px!important;  position: relative; bottom: .2em; }*/
    .nowrap {
        white-space: nowrap;
    }
/*@media screen and (max-width: 600px) {
.campaign-clock .digit {
	width: 1.55em;
}
}         
@media screen and (min-width: 601px) {
    .campaign-clock .digit {
	    width: 1.25em;
    }
}*/
</style>
<script src="https://cdn.jsdelivr.net/npm/easytimer.js@4.6.0/dist/easytimer.min.js"></script><link rel="preconnect" href="https://fonts.googleapis.com">
<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
<link href="https://fonts.googleapis.com/css2?family=Big+Shoulders+Display:wght@100..900&display=swap" rel="stylesheet"><style>

.zen-body__main { padding: 0}
.zen-breadcrumbs {display:none}
.sp-page-builder .page-content .sppb-column-addons .sppb-section.holiday-row {
  background-color: #f2f2f2!important;
    width: 100%!important;
    max-width: 100%!important;
    padding: 0!important;
}
.sp-page-builder .page-content .sppb-column-addons .sppb-section.holiday-row:first-of-type, #section-id-1730721246030 {
  padding-top: 50px!important;
}
.sp-page-builder .page-content .sppb-column-addons .sppb-section.holiday-row:last-of-type, #section-id-1730719053242 {
  padding-top: 20px!important;
padding-bottom: 50px!important;
}



.margin-auto { margin: 0 auto!important;}
.display-block { display: block; }
.fit-content { width: fit-content;}

.campaign-corner-flash {width: 150px; height: 150px;}

/*
.sppb-column-addons .sppb-addon-wrapper, .sppb-column-addons .sppb-addon-wrapper div:not(.zen-card__info):not(.zen-text--text-df):not(.zen-flex-end) {margin-bottom: 0; height: 100% }
.sppb-column-addons .sppb-addon-wrapper > .sppb-addon {height: 100% }
.zen-card.zen-card--related {margin-bottom: 0}
.zen-card.zen-card--related .zen-link {height: 100%}
.sppb-column-addons .sppb-addon-wrapper .zen-flex-end { height: auto!important; }
.holiday-row .sppb-row > [class^="sppb-col"] {margin-bottom: 20px; }
*/

@media screen and (max-width: 450px) {

  .page-content .sppb-section::not(.holiday-row) { padding: 0 0 50px 0!important;}
  .sppb-section.holiday-row .sppb-column { padding: 0 15px; }
  .campaign-corner-flash {width: 100px; height: 100px;}

  .zen-footer { text-align: center; }
  .zen-footer .sppb-addon-single-image-container { margin: 0 auto; display:block; width: fit-content;}

}

@media screen and (min-width: 451px) {

  .sppb-section.holiday-row .sppb-container-inner {
    width: 65%;
    max-width: 1120px;
    margin: 0 auto;
  }

  .sp-page-builder .page-content .sppb-column-addons .sppb-section.holiday-row {

  }

}
@media (min-width: 500px) and (max-width: 767px) {
.sppb-addon-sp-slider .sp-slider-outer-stage, div.sp-slider.on-3d-active .sp-slider-outer-stage {
  height: 580px!important;
}
}

@media (min-width: 768px) and (max-width: 991px) {
    .sp-page-builder .page-content #section-id-1733150149652 {
        margin-top: 170px!important;
    }
}

@media screen and (min-width: 344px) and (max-width: 430px) and (min-height: 700px) and (max-height: 932px) {
    .sppb-addon-sp-slider .sp-slider-outer-stage, div.sp-slider.on-3d-active .sp-slider-outer-stage {
        height: 555px!important;
    }
    #sppb-addon-1733150149659 { padding-top: 65px!important; }
}
@media screen and (min-width: 429px) and (max-width: 450px) and (min-height: 740px) and (max-height: 932px) {
#sppb-addon-1733150149659 {
    padding-top: 46px!important;
}
.sppb-addon-sp-slider .sp-slider-outer-stage, div.sp-slider.on-3d-active .sp-slider-outer-stage {
    height: 580px!important;
}
}

@media screen and (min-width: 451px) and (max-width: 767px) and (min-height: 740px) and (max-height: 932px) {
#sppb-addon-1733150149659 {
    padding-top: 40px!important;
}
.sppb-addon-sp-slider .sp-slider-outer-stage, div.sp-slider.on-3d-active .sp-slider-outer-stage {
    height: 580px!important;
}
}

</style>
            <div class="container mb-4">
        <div class="row">
                  </div>
      </div>
    </footer>

    </div><!-- /#page -->

    <!------------------------------------------------------------------------------
    // Modals - Placed outside page wrapper for proper stacking
    //---------------------------------------------------------------------------->
    <div id="modals" class="zen-modals">
      
      <div class="modal modal-brochure fade"
      id="brochureModal"
      tabindex="3"
      role="dialog"
      aria-hidden="true"
      >
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-body p-3">
              <button type="button" class="zen-btn mb-2" data-bs-dismiss="modal">&times;</button>
                
            </div>
          </div>
        </div>
      </div>
    </div>

    <!------------------------------------------------------------------------------
    // Back to Top Button
    //---------------------------------------------------------------------------->
    <div class="zen-back-to-top">
      <button class="zen-btn zen-btn--scroll js-zen-back-to-top">
        <em class="fontello icon-chevron-up"></em>
      </button>
    </div>

    <!------------------------------------------------------------------------------
    // Cookie Policy
    //---------------------------------------------------------------------------->
    <div class="zen-panel zen-panel--policy" style="display: none;">
      <div class="container">
        <div class="row">
          <div class="col-12">
            <p class="zen-text zen-text--lead mb-2">
              Cookie Policy              <span class="zen-cta float-right p-1 mt-n1 js-cookie-policy">
                <em class="fontello icon-sys-close"></em>
              </span>
            </p>
          </div>
          <div class="col-12">
            <div class="zen-panel__content">
              <div class="row">
                <div class="col-sm-9 col-md-10 d-inline-block">
                  <p class="zen-text mb-2">
                    We use cookies to ensure that we give you the best experience on our website. If you continue to use this site we will assume that you are happy with this.                  </p>
                </div>
                <div class="col-sm-3 col-md-2 d-inline-block">
                  <p class="mb-0">
                    <button class="zen-btn zen-btn--full-size zen-btn--text-xs px-3 js-cookie-policy">
                      Continue                    </button>
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Start of LiveChat (www.livechat.com) code -->
    <script>
        window.__lc = window.__lc || {};
        window.__lc.license = 18020283;
        window.__lc.integration_name = "manual_channels";
        window.__lc.product_name = "livechat";
        ;(function(n,t,c){function i(n){return e._h?e._h.apply(null,n):e._q.push(n)}var e={_q:[],_h:null,_v:"2.0",on:function(){i(["on",c.call(arguments)])},once:function(){i(["once",c.call(arguments)])},off:function(){i(["off",c.call(arguments)])},get:function(){if(!e._h)throw new Error("[LiveChatWidget] You can't use getters before load.");return i(["get",c.call(arguments)])},call:function(){i(["call",c.call(arguments)])},init:function(){var n=t.createElement("script");n.async=!0,n.type="text/javascript",n.src="https://cdn.livechatinc.com/tracking.js",t.head.appendChild(n)}};!n.__lc.asyncInit&&e.init(),n.LiveChatWidget=n.LiveChatWidget||e}(window,document,[].slice))
    </script>
    <noscript><a href="https://www.livechat.com/chat-with/18020283/" rel="nofollow">Chat with us</a>, powered by <a href="https://www.livechat.com/?welcome" rel="noopener nofollow" target="_blank">LiveChat</a></noscript>
    <!-- End of LiveChat code -->
    <script>
              // ON LOAD

              function etOnReady(initialData) {

                // Chat Widget is ready

                var state = initialData.state;
                var customerData = initialData.customerData;

                /* Hide greeting */
                window.LiveChatWidget.call("hide_greeting");

                // Change mobile bar chat button from inactive to active
                document.querySelector('.mobile-bar .mobile-chat-button').removeAttribute('disabled');

                document.querySelector('.mobile-chat-button').addEventListener('click', function(e) {
                  e.preventDefault();
                  var state = window.LiveChatWidget.get("state");
                  if(state.visibility === 'maximized') {
                    // Chat is active
                    window.LiveChatWidget.call("minimize");
                    // Allow mobile bar to show if needed
                  } else {
                    // Chat is inactive
                    window.LiveChatWidget.call("maximize");
                    // Hide mobile-bar and keep it hidden
                    document.querySelector('.mobile-bar').classList.add('mobile-bar-hide');
                    document.querySelector('.mobile-bar').classList.remove('mobile-bar-show');
                  }
              });
      }
      window.LiveChatWidget.on('ready', etOnReady);

    jQuery(function() {
/* CHAT */
        /* chat functions for mobile bar */
        /* https://help.gohighlevel.com/support/solutions/articles/48001191051-web-chat-widget-advanced-configurations-public-api-events */

        /* SCROLL FUNCTIONS */

        var mobileBarElement = document.querySelector('.mobile-bar');
        window.mobileBar = window.mobileBar || {};
        if (mobileBarElement !== null) {
            window.mobileBar.status = (window.getComputedStyle(mobileBarElement).display == 'block' || window.getComputedStyle(mobileBarElement).display == 'flex') ? 'open' : 'closed';
        }


        /* Hide header on scroll down, show on scroll up */
        /* Credit: https://twitter.com/mariusc23 */
        // Hide Header on on scroll down
        var didScroll,
            lastScrollTop = 0,
            delta = 5,
            windowInnerHeight = window.innerHeight,
            mobileBarHeight = jQuery('.mobile-bar').outerHeight();

        jQuery(window).scroll(function (event) {
            didScroll = true;
        });

        setInterval(function () {
            if (didScroll) {
                hasScrolled();
                didScroll = false;
            }
        }, 250);


        function hasScrolled() {
            var st = jQuery(this).scrollTop();

            if (jQuery('.mobile.bar').is(':visible')) {
                jQuery('.mobile-bar').removeClass('mobile-bar-show').addClass('mobile-bar-hide'); //hide mobile bar
                const event = new Event('mobilebar:hide');
                window.dispatchEvent(event);
                window.mobileBar = window.mobileBar || {};
                window.mobileBar.status = 'closed';
            } else {
                if (st > lastScrollTop) {
                    // Scroll Down
                    // Show bar
                    if (!jQuery('.mobile-bar').hasClass('mobile-bar-show') &&
                        typeof window.LiveChatWidget !== 'undefined' &&
                        window.LiveChatWidget._h) { // Check if widget is loaded

                        try {
                            var state = window.LiveChatWidget.get("state");
                            if (state.visibility === 'minimized') {
                                jQuery('.mobile-bar').addClass('mobile-bar-show').removeClass('mobile-bar-hide');
                                const event = new Event('mobilebar:show');
                                window.dispatchEvent(event);
                                window.mobileBar = window.mobileBar || {};
                                window.mobileBar.status = 'open';
                            }
                        } catch (e) {
                            console.debug('LiveChat not ready yet');
                        }
                    }
                } else {
                    // Scroll Up
                    // Hide bar - only if chat window not open
                    jQuery('.mobile-bar').removeClass('mobile-bar-show').addClass('mobile-bar-hide');
                    const event = new Event('mobilebar:hide');
                    window.dispatchEvent(event);
                    window.mobileBar = window.mobileBar || {};
                    window.mobileBar.status = 'closed';
                }
            }

            if (Math.abs(lastScrollTop - st) <= delta)
                return;

            lastScrollTop = st;
        }

      /* MENU FIXES */
                document.querySelectorAll('.zen-menu__main .nav-item a[href*="/destinations-menu"]').forEach(link => {
                  // console.log(link);
                  link.href = link.href.replace('destinations-menu','destinations')
                });
                      // Removing the event listener that prevents collection links from working
	});

  function appendQueryParamsToLinks(linkSelector) {
            const links = document.querySelectorAll(linkSelector);
            if (!links.length) return;

            const currentUrl = new URL(window.location.href);

            links.forEach(link => {
                const linkUrl = new URL(link.href);

                currentUrl.searchParams.forEach((value, key) => {
                  if (key.startsWith('utm') || key.startsWith('gad') || key.startsWith('gclid')) {
                        linkUrl.searchParams.set(encodeURIComponent(key), encodeURIComponent(value));
                    }
                });

                link.href = linkUrl.toString();
            });
        }

        document.addEventListener('DOMContentLoaded', function() {
            const currentUrlPath = window.location.pathname;
            if (currentUrlPath.startsWith('/ppc-landing')) {
                appendQueryParamsToLinks('.ppc-brochure');
            }
        });
</script>
<style>
.zen-holiday__tabs {
top: 107px;
}
</style>
    <!------------------------------------------------------------------------------
    // Debug Area
    //----------------------------------------------------------------------------->
    
    <script src="/templates/zenbase/js/zen-lazy-load.js" type="text/javascript"></script>

    <!-- mmenu JavaScript -->
    <script src="/templates/zenbase/mmenu/dist/mmenu.js"></script>
    <script>
      document.addEventListener('DOMContentLoaded', () => {
        const menu = new Mmenu('#mobile-menu', {
          // Core options
          slidingSubmenus: true,
          // Off-canvas configuration
          offCanvas: {
            position: 'right-front'
          },
          "theme": "dark",
          // Navigation configuration
          navbar: {
            title: 'Menu'
          },
          navbars: [{
            position: 'top',
            content: ['prev', 'title']
          }],
          // Visual configuration
          extensions: [
            'pagedim-black',
            'position-right',
            'fx-menu-slide'
          ],
          // Disable automatic panel linking for specific items
          onClick: {
            preventDefault: function(anchor) {
              // Check if this is a Destinations or Collections item
              return anchor.parentElement.classList.contains('no-click');
            }
          }
        }, {
          // Configuration object
          offCanvas: {
            page: {
              selector: '#page'
            }
          },
          classNames: {
            selected: "Selected"
          }
        });

        // Get the API
        const api = menu.API;

        // Add click handler for the menu trigger
        document.getElementById('mobile-menu-trigger').addEventListener('click', (e) => {
          e.preventDefault();
          api.open();
        });
      });
    </script>
  <div data-id="5" 
	class="eb-inst eb-hide eb-custom eb-5 "
	data-options='{"trigger":"onClick","trigger_selector":"#openCampaignModal","delay":0,"scroll_depth":"percentage","scroll_depth_value":80,"firing_frequency":1,"reverse_scroll_close":false,"threshold":0,"close_out_viewport":false,"exit_timer":1000,"idle_time":10000,"prevent_default":true,"close_on_esc":false,"animation_open":"transition.slideUpIn","animation_close":"transition.fadeOut","animation_duration":300,"disable_page_scroll":false,"test_mode":false,"debug":false,"ga_tracking":false,"ga_tracking_id":0,"ga_tracking_event_category":"EngageBox","ga_tracking_event_label":"Box #5 - The Not-So-Small Print (T&Cs):","auto_focus":false}'
	data-type='popup'
		>

	<button type="button" data-ebox-cmd="close" class="eb-close placement-outside" aria-label="Close">
	<img alt="close popup button" />
	<span aria-hidden="true">&times;</span>
</button>
	<div class="eb-dialog " role="dialog" aria-modal="true" id="dialog5" aria-label="dialog5">
		
		<button type="button" data-ebox-cmd="close" class="eb-close placement-inside" aria-label="Close">
	<img alt="close popup button" />
	<span aria-hidden="true">&times;</span>
</button>	
		<div class="eb-container">
							<div class="eb-header">
					The Not-So-Small Print (T&Cs):				</div>
						<div class="eb-content">
				<ul>
<li>Your £75 Ellis Brigham gift voucher will be sent to you 5 months before your trek start date.</li>
<li>The voucher cannot be exchanged for cash, used in conjunction with other offers, or transferred to another person.</li>
<li>If you reschedule or cancel your trek, the £100 discount and gift voucher will no longer be valid.</li>
<li>Offer is valid for new Nepal trek bookings made between 5th and 16th May 2025.</li>
</ul>			</div>
		</div>
	</div>	
</div>
</body>
</html>
