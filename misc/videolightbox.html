<div class="modal fade" id="videoLightbox" tabindex="-1" aria-labelledby="videoLightboxLabel" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered modal-xl">
    <div class="modal-content">
      <div class="modal-body">
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
        <div class="ratio ratio-16x9">
          <iframe src="https://www.youtube.com/embed/l1hU7Od2X-w?enablejsapi=1" allowfullscreen allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"></iframe>
        </div>
        <div class="mt-3">
          <div class="row row-cols-1 row-cols-md-2 row-cols-lg-4 g-2">
            <!-- Column 1 -->
            <div class="col">
              <div class="row g-2">
                <div class="col-2 px-md-2 col-md-3">
                  <img class="sppb-img-responsive " alt="Image" title="" style=""
                    src="/templates/zenbase/icons/choose-rosette.svg">
                </div>
                <div class="col-10 px-md-2 col-md-9">
                  <p class="mb-0">Unmatched Expertise and Extensive Experience</p>
                </div>
              </div>
            </div>
            <!-- Column 2 -->
            <div class="col">
              <div class="row g-2">
                <div class="col-2 px-md-2 col-md-3">
                  <img class="sppb-img-responsive " alt="Image" title="" style=""
                    src="/templates/zenbase/icons/choose-clipboard.svg">
                </div>
                <div class="col-10 px-md-2 col-md-9">
                  <p class="mb-0">Comprehensive Preparation and Support</p>
                </div>
              </div>
            </div>
            <!-- Column 3 -->
            <div class="col">
              <div class="row g-2">
                <div class="col-2 px-md-2 col-md-3">
                  <img class="sppb-img-responsive " alt="Image" title="" style=""
                    src="/templates/zenbase/icons/choose-seedling.svg">
                </div>
                <div class="col-10 px-md-2 col-md-9">
                  <p class="mb-0">Local Guides and Environmental Responsibility</p>
                </div>
              </div>
            </div>
            <!-- Column 4 -->
            <div class="col">
              <div class="row g-2">
                <div class="col-2 px-md-2 col-md-3">
                  <img class="sppb-img-responsive " alt="Image" title="" style=""
                    src="/templates/zenbase/icons/choose-handshake.svg">
                </div>
                <div class="col-10 px-md-2 col-md-9">
                  <p class="mb-0">World-Class Customer Service and Financial Flexibility</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <style>
    #videoLightbox .ratio {
      background-color: black;
      border-radius: 8px;
      overflow: hidden;
    }

    #videoLightbox .ratio iframe {
      z-index: 2;
    }

    #videoLightbox .modal-xl {
      max-width: 70%;
      width: 70%;
    }

    #videoLightbox .modal-body {
      padding: 0;
      background-color: transparent;
      position: relative;
    }

    #videoLightbox .btn-close {
      position: absolute;
      right: 15px;
      top: 15px;
      color: white;
      font-size: 1.25rem;
      opacity: 1;
      z-index: 3;
      background: transparent url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16' fill='%23fff'%3e%3cpath d='M.293.293a1 1 0 011.414 0L8 6.586 14.293.293a1 1 0 111.414 1.414L9.414 8l6.293 6.293a1 1 0 01-1.414 1.414L8 9.414l-6.293 6.293a1 1 0 01-1.414-1.414L6.586 8 .293 1.707a1 1 0 010-1.414z'/%3e%3c/svg%3e") center/1em auto no-repeat;
    }

    #videoLightbox .modal-content {
      background-color: transparent!important;
      border: none;
      font-size: 14.5px;
      line-height: normal;
      font-weight: bold;
    }

    #videoLightbox .mt-3 {
      background-color: transparent;
      color: white;
    }

    #videoLightbox .row:not(.justify-content-center) .col-8 {
      flex: 0 0 66.666667%;
      max-width: 66.666667%;
    }

    #videoLightbox .row:not(.justify-content-center) .col-4 {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }

    #videoLightbox .row:not(.justify-content-center) .col-4.col-md-12 {
      flex: 0 0 33.333333%;
      max-width: 33.333333%;
    }

    @media (max-width: 767px) {
      #videoLightbox .modal-dialog {
        width: 95%;
        max-width: 95%;
        margin: 0 auto;
      }

      #videoLightbox .video-container {
        width: 100%;
      }

      #videoLightbox .modal-xl {
        max-width: 95%;
        width: 95%;
      }

      #videoLightbox .col-10.px-md-2.col-md-9 {
        padding-left: 20px;
        padding-right: 30px;
        display: flex;
        align-items: center;
      }

      #videoLightbox .col-2.px-md-2.col-md-3 {
        padding-right: 0;
      }

      #videoLightbox .col-2.px-md-2.col-md-3 img {
        width: 42px;
        max-width: 42px;
        height: auto;
      }
    }

    #videoLightbox .mt-3>.row {
      margin-left: 0;
      margin-right: 0;
    }
    #videoLightbox .col {
      margin-top: 15px;
    }

    #videoLightbox .modal-content img {
      height: 63px;
      width: auto;
    }

    #videoLightbox .col-2.px-md-2.col-md-3 {
      display: flex;
      justify-content: flex-end;
      padding-right: 10px !important;
      padding-left: 0;
    }

    @media screen and (min-width: 768px) {

    }
  </style>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const videoLightbox = document.getElementById('videoLightbox');
      if (videoLightbox) {
        // Function to detect mobile devices
        function isMobileDevice() {
          return window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        }

        videoLightbox.addEventListener('show.bs.modal', function (event) {
          const iframe = this.querySelector('iframe');
          if (iframe) {
            let currentSrc = iframe.src;

            // Add autoplay parameter for mobile devices only
            if (isMobileDevice()) {
              // Check if URL already has parameters
              const separator = currentSrc.includes('?') ? '&' : '?';
              // Add autoplay=1 if not already present
              if (!currentSrc.includes('autoplay=')) {
                currentSrc += separator + 'autoplay=1';
              }
            } else {
              // Remove autoplay parameter for desktop
              currentSrc = currentSrc.replace(/[?&]autoplay=1/g, '');
            }

            // Reload the iframe with the modified URL
            iframe.src = currentSrc;
          }
        });

        videoLightbox.addEventListener('hide.bs.modal', function (event) {
          const iframe = this.querySelector('iframe');
          if (iframe) {
            // Pause the video when modal is closed
            iframe.src = iframe.src;
          }
        });
      }
    });
  </script>